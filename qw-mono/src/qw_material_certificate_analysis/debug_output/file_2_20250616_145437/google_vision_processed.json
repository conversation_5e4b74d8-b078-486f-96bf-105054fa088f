{"text": "MARCEGAGLIA\nPLATES\nCustomer/Kunde\nJEBENS GMBH\nDAIMLERSTR.35-37\n70825 KORNTAL-MUENCHINGEN DE\nMaterial\nLTS\nDescription/Beschreibung\n20,00 X 2450,00 X 12000,00 S355J2+N\nItem Batch/Bund Nr\nHeat/Schmelze\n1\n24EH004442\n338783\n2\n24EH004443\n143316\nDieses Dokument wurde automatisch erstellt und ist ohne\nUnterschrift gültig\nNumber/Nummer ***********\nAusgegeben am 05/08/2024\nMarcegaglia\n46040 Via Bresciani, 16-<PERSON>az<PERSON><PERSON> degli Ippoliti Mantova-Italy\nTel. +39-0376 685 1 Fax. +39-0376 685 600\nwww.marcegaglia.com\nStab. di S.Giorgio di Nogaro:\nvia <PERSON>i 33, 33058 S.Giorgio di Nogaro-Udine\ntel+390431624111 fax 0431624222\nType/Typ\nAbnahmeprüfzeugnis 3.1 EN 10204\nProduct: Hot Rolled Heavy Plates\nNormaCollaudo Lamiera Treno: EN10025-2:2019\nSteel grade:: S355J2+N\nCode: 0000036345\nDel. Address/Anlieferadresse\nDelivery/Lieferung nr\n8930483286\nJEBENS GMBH\nOf/Vom\n05/08/2024\nDAIMLERSTR.35-37\n70825 KORNTAL-MUENCHINGEN DE\nDelivery note nr/Lieferschein nr\n1060009915\nSurface Quality: EN10163-2 CL A1\nFlatness: rif EN 10029 cl. N\nToleranzen: EN10029 CLASSE B\nDelivery Condition: NORMALIZING ROLLING\nSteel Making Process: BO= Basic oxigen\nQuality Control/Qualitaetsicherung\nQ.M.D./Bearb. M.Quargnali\nPlant/Werk S. Giorgio di Nogaro\nOrder Nr/Auftrag Nr\n1191789210/40\nPart Number\nPages/Seite\n1/2\nClient Order/Kundenauftrag\n61039475\nClient Date/Kundendatum\n18/07/2024\nQuantity/Anzahl KG\nQuantity/Anzahl PZZ\n4704\n4704\n1\n1\nRemarks/Bemerkungen:\nThe Declaration of Performance (DOP) can be downloaded from the following web page:\nwww.quality.marcegaglia.com/marcegaglia-plates-dop\nс\nMn\nSi\nBatch/Bund Nr\n(%)\n(%)\n(%)\nཨཙྩེ\nS\n(%)\nP\n(%)\n0.2\n1.6\nવ\n0.55\n0.025\nn  ིི།\nCr\nNi\nCu\nAl\nMo\nNb\n(%)\n(%)\n(%)\n(%)\n(%)\n(%)\nརྩེད\n(%)\nTi\n(%)\nN\nZ\nCeq\n(%)\n(%)\n0.02\n0.025\n0.3\n0.3\n0.55\n0.3\n0.08\n0.06\n0.1\n0.05\n0.012\n0.45\n24EH004442 .150 1.500 .230\n24EH004443\n.150\n1.510 .230\n.0060\n.0160\n.020\n.010\n.020\n.034\n.0000\n.032\n.001\n.0030 .0020 .41\n.0050 .0160\n.010\n.010\n.010\n.038\n.0000\n.030\n.001\n.0040 .0020\n.41\nForma\n(1) (2) (3)\nT\nBatch/Bund Nr\n20\n- ຂົສ ສ ສ ສ\nRm\nReH\n[°C] [N/mmq]\n[N/mmq]\n470\n345\nA%\n(%)\n20\n(1b (2b) (3b (4b)\n630\n547\n434\n28.1\n1\nP L 10.0\n549\n449\n28.7\n1\nPL 10.0\n1888\nT Kv1 Kv2\nKv3\nKv\n[°C] [J] [J] [J] [J]\n-60 27 27 27\n드줄\nTESTN\n-20\n-20 242 220 201 221 24-63696\n-20 238 245 224 235 24-63693\n24EH004442\nP\n1\nP T 20\n24EH004443\nP\n1\nPT 20\nUltrasonic control\nFirma\nEquipment\nSerial number\nSurface conditions\nGilardoni RDG/700\n1001288\nAs Rolled\nCouplant\nWater\nProcedure\nAccording To Specification\nCalibration\nAccording To Specification\nIIllivEN/ISO9712\nQ.M.D. M.Quargnali\nItem\nStandard of reference\n1\nEN10160\n2\nEN10160\nAcceptance criteria\nCL.S1 E1\nCL.S1 E1\nResult\nIndications\nOk\nOk\nNot founded\nNot founded\nReticle\n200x200mm\n200x200mm\nEdges\n50\n50\nProbe\nBDD20/4\nBDD20/4\nFrequency\n4 MHz\n4 MHz\nAngle\n0°\n0°\nMaterial Free of radioactive contamination\nDimensional check and visual examination of the surface condition: without objection\nWe Declare that the above mentioned plates are in compliance with the order prescription\n-We hereby declare that the above mentioned material have been rolled in Italy, thus of European Community origin\nPLATES\nMARCEGAGLIA\nMichele Dumpli\n(1)Location:1-Top:2-Bottom (2)Location:1/2-1/2 thickness;P-sourface:1/4-1/4 thickness.(3)Direction:L-Longitudinal; T-Trasverse:(4)Specimen Thickness\nUK\nCA\n1244\n22\nDescrizioneMarchi\nMARCEGAGLIA\nPLATES\nv. Enrico Fermi, 33\n33058 S. Giorgio Nogaro\nUdine-Italy\nEN 10025-1\nEN 10025-2\n0474\nMARCEGAGLIA\nv. Enrico Fermi, 33\nCE\nPLATES\nnio Nogaro\n33058-S. Giorgio\nUdine-Italy\nEN 10025-1\nEN 10025-2", "text_blocks": [{"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 90, "y": 49}, {"x": 374, "y": 49}, {"x": 374, "y": 74}, {"x": 90, "y": 74}]}, "elements": [{"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 90, "y": 49}, {"x": 374, "y": 49}, {"x": 374, "y": 74}, {"x": 90, "y": 74}]}}]}, {"text": "PLATES", "bounding_poly": {"vertices": [{"x": 294, "y": 82}, {"x": 373, "y": 82}, {"x": 373, "y": 95}, {"x": 294, "y": 95}]}, "elements": [{"text": "PLATES", "bounding_poly": {"vertices": [{"x": 294, "y": 82}, {"x": 373, "y": 82}, {"x": 373, "y": 95}, {"x": 294, "y": 95}]}}]}, {"text": "Customer / <PERSON>nde \nJ<PERSON>NS GMBH \nDAIMLERSTR.35-37 \n70825 KORNTAL - MUENCHINGEN DE", "bounding_poly": {"vertices": [{"x": 38, "y": 149}, {"x": 314, "y": 149}, {"x": 314, "y": 212}, {"x": 38, "y": 212}]}, "elements": [{"text": "Customer", "bounding_poly": {"vertices": [{"x": 38, "y": 149}, {"x": 90, "y": 149}, {"x": 90, "y": 158}, {"x": 38, "y": 158}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 88, "y": 149}, {"x": 92, "y": 149}, {"x": 92, "y": 158}, {"x": 88, "y": 158}]}}, {"text": "Kunde", "bounding_poly": {"vertices": [{"x": 92, "y": 149}, {"x": 128, "y": 149}, {"x": 128, "y": 158}, {"x": 92, "y": 158}]}}, {"text": "JEBENS", "bounding_poly": {"vertices": [{"x": 38, "y": 163}, {"x": 98, "y": 163}, {"x": 98, "y": 176}, {"x": 38, "y": 176}]}}, {"text": "GMBH", "bounding_poly": {"vertices": [{"x": 104, "y": 163}, {"x": 150, "y": 163}, {"x": 150, "y": 175}, {"x": 104, "y": 175}]}}, {"text": "DAIMLERSTR.35-37", "bounding_poly": {"vertices": [{"x": 39, "y": 182}, {"x": 186, "y": 182}, {"x": 186, "y": 194}, {"x": 39, "y": 194}]}}, {"text": "70825", "bounding_poly": {"vertices": [{"x": 38, "y": 200}, {"x": 81, "y": 200}, {"x": 81, "y": 212}, {"x": 38, "y": 212}]}}, {"text": "KORNTAL", "bounding_poly": {"vertices": [{"x": 87, "y": 200}, {"x": 161, "y": 200}, {"x": 161, "y": 212}, {"x": 87, "y": 212}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 162, "y": 200}, {"x": 167, "y": 200}, {"x": 167, "y": 212}, {"x": 162, "y": 212}]}}, {"text": "MUENCHINGEN", "bounding_poly": {"vertices": [{"x": 169, "y": 200}, {"x": 288, "y": 200}, {"x": 288, "y": 212}, {"x": 169, "y": 212}]}}, {"text": "DE", "bounding_poly": {"vertices": [{"x": 294, "y": 200}, {"x": 314, "y": 200}, {"x": 314, "y": 212}, {"x": 294, "y": 212}]}}]}, {"text": "Material \nLTS", "bounding_poly": {"vertices": [{"x": 42, "y": 225}, {"x": 85, "y": 225}, {"x": 85, "y": 250}, {"x": 42, "y": 250}]}, "elements": [{"text": "Material", "bounding_poly": {"vertices": [{"x": 42, "y": 225}, {"x": 85, "y": 225}, {"x": 85, "y": 234}, {"x": 42, "y": 234}]}}, {"text": "LTS", "bounding_poly": {"vertices": [{"x": 42, "y": 238}, {"x": 71, "y": 238}, {"x": 71, "y": 250}, {"x": 42, "y": 250}]}}]}, {"text": "Description / Beschreibung", "bounding_poly": {"vertices": [{"x": 43, "y": 263}, {"x": 178, "y": 264}, {"x": 178, "y": 276}, {"x": 43, "y": 275}]}, "elements": [{"text": "Description", "bounding_poly": {"vertices": [{"x": 43, "y": 264}, {"x": 101, "y": 264}, {"x": 101, "y": 275}, {"x": 43, "y": 275}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 101, "y": 264}, {"x": 105, "y": 264}, {"x": 105, "y": 275}, {"x": 101, "y": 275}]}}, {"text": "Beschreibung", "bounding_poly": {"vertices": [{"x": 105, "y": 263}, {"x": 178, "y": 264}, {"x": 178, "y": 276}, {"x": 105, "y": 275}]}}]}, {"text": "20,00 X 2450,00 X 12000,00 S355J2 + N", "bounding_poly": {"vertices": [{"x": 41, "y": 277}, {"x": 324, "y": 276}, {"x": 324, "y": 291}, {"x": 41, "y": 292}]}, "elements": [{"text": "20,00", "bounding_poly": {"vertices": [{"x": 41, "y": 277}, {"x": 80, "y": 277}, {"x": 80, "y": 292}, {"x": 41, "y": 292}]}}, {"text": "X", "bounding_poly": {"vertices": [{"x": 85, "y": 277}, {"x": 95, "y": 277}, {"x": 95, "y": 291}, {"x": 85, "y": 291}]}}, {"text": "2450,00", "bounding_poly": {"vertices": [{"x": 100, "y": 277}, {"x": 158, "y": 277}, {"x": 158, "y": 291}, {"x": 100, "y": 291}]}}, {"text": "X", "bounding_poly": {"vertices": [{"x": 162, "y": 277}, {"x": 173, "y": 277}, {"x": 173, "y": 291}, {"x": 162, "y": 291}]}}, {"text": "12000,00", "bounding_poly": {"vertices": [{"x": 179, "y": 277}, {"x": 244, "y": 277}, {"x": 244, "y": 291}, {"x": 179, "y": 291}]}}, {"text": "S355J2", "bounding_poly": {"vertices": [{"x": 249, "y": 277}, {"x": 304, "y": 277}, {"x": 304, "y": 291}, {"x": 249, "y": 291}]}}, {"text": "+", "bounding_poly": {"vertices": [{"x": 303, "y": 277}, {"x": 312, "y": 277}, {"x": 312, "y": 291}, {"x": 303, "y": 291}]}}, {"text": "N", "bounding_poly": {"vertices": [{"x": 313, "y": 276}, {"x": 324, "y": 276}, {"x": 324, "y": 290}, {"x": 313, "y": 290}]}}]}, {"text": "Item Batch / Bund Nr", "bounding_poly": {"vertices": [{"x": 42, "y": 313}, {"x": 164, "y": 313}, {"x": 164, "y": 322}, {"x": 42, "y": 322}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 42, "y": 314}, {"x": 63, "y": 314}, {"x": 63, "y": 322}, {"x": 42, "y": 322}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 86, "y": 313}, {"x": 116, "y": 313}, {"x": 116, "y": 322}, {"x": 86, "y": 322}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 116, "y": 313}, {"x": 120, "y": 313}, {"x": 120, "y": 322}, {"x": 116, "y": 322}]}}, {"text": "Bund", "bounding_poly": {"vertices": [{"x": 120, "y": 313}, {"x": 147, "y": 313}, {"x": 147, "y": 322}, {"x": 120, "y": 322}]}}, {"text": "Nr", "bounding_poly": {"vertices": [{"x": 151, "y": 313}, {"x": 164, "y": 313}, {"x": 164, "y": 322}, {"x": 151, "y": 322}]}}]}, {"text": "Heat / Schmelze", "bounding_poly": {"vertices": [{"x": 217, "y": 313}, {"x": 296, "y": 313}, {"x": 296, "y": 322}, {"x": 217, "y": 322}]}, "elements": [{"text": "Heat", "bounding_poly": {"vertices": [{"x": 217, "y": 313}, {"x": 241, "y": 313}, {"x": 241, "y": 322}, {"x": 217, "y": 322}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 241, "y": 313}, {"x": 245, "y": 313}, {"x": 245, "y": 322}, {"x": 241, "y": 322}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 244, "y": 313}, {"x": 296, "y": 313}, {"x": 296, "y": 322}, {"x": 244, "y": 322}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 42, "y": 335}, {"x": 49, "y": 335}, {"x": 49, "y": 344}, {"x": 42, "y": 344}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 42, "y": 335}, {"x": 49, "y": 335}, {"x": 49, "y": 344}, {"x": 42, "y": 344}]}}]}, {"text": "24EH004442", "bounding_poly": {"vertices": [{"x": 86, "y": 334}, {"x": 178, "y": 334}, {"x": 178, "y": 346}, {"x": 86, "y": 346}]}, "elements": [{"text": "24EH004442", "bounding_poly": {"vertices": [{"x": 86, "y": 334}, {"x": 178, "y": 334}, {"x": 178, "y": 346}, {"x": 86, "y": 346}]}}]}, {"text": "338783", "bounding_poly": {"vertices": [{"x": 216, "y": 334}, {"x": 269, "y": 333}, {"x": 269, "y": 345}, {"x": 216, "y": 346}]}, "elements": [{"text": "338783", "bounding_poly": {"vertices": [{"x": 216, "y": 334}, {"x": 269, "y": 333}, {"x": 269, "y": 345}, {"x": 216, "y": 346}]}}]}, {"text": "2", "bounding_poly": {"vertices": [{"x": 41, "y": 359}, {"x": 51, "y": 359}, {"x": 51, "y": 369}, {"x": 41, "y": 369}]}, "elements": [{"text": "2", "bounding_poly": {"vertices": [{"x": 41, "y": 359}, {"x": 51, "y": 359}, {"x": 51, "y": 369}, {"x": 41, "y": 369}]}}]}, {"text": "24EH004443", "bounding_poly": {"vertices": [{"x": 86, "y": 358}, {"x": 177, "y": 356}, {"x": 177, "y": 369}, {"x": 86, "y": 371}]}, "elements": [{"text": "24EH004443", "bounding_poly": {"vertices": [{"x": 86, "y": 358}, {"x": 177, "y": 356}, {"x": 177, "y": 369}, {"x": 86, "y": 371}]}}]}, {"text": "143316", "bounding_poly": {"vertices": [{"x": 217, "y": 359}, {"x": 269, "y": 359}, {"x": 269, "y": 370}, {"x": 217, "y": 370}]}, "elements": [{"text": "143316", "bounding_poly": {"vertices": [{"x": 217, "y": 359}, {"x": 269, "y": 359}, {"x": 269, "y": 370}, {"x": 217, "y": 370}]}}]}, {"text": "Dieses Dokument wurde automatisch erstellt und ist ohne Unterschrift gültig", "bounding_poly": {"vertices": [{"x": 950, "y": 29}, {"x": 1153, "y": 31}, {"x": 1153, "y": 55}, {"x": 950, "y": 53}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 950, "y": 32}, {"x": 974, "y": 32}, {"x": 974, "y": 41}, {"x": 950, "y": 41}]}}, {"text": "Dokument", "bounding_poly": {"vertices": [{"x": 975, "y": 32}, {"x": 1012, "y": 32}, {"x": 1012, "y": 40}, {"x": 975, "y": 40}]}}, {"text": "wurde", "bounding_poly": {"vertices": [{"x": 1014, "y": 32}, {"x": 1036, "y": 32}, {"x": 1036, "y": 40}, {"x": 1014, "y": 40}]}}, {"text": "automatisch", "bounding_poly": {"vertices": [{"x": 1038, "y": 32}, {"x": 1081, "y": 32}, {"x": 1081, "y": 40}, {"x": 1038, "y": 40}]}}, {"text": "erstellt", "bounding_poly": {"vertices": [{"x": 1083, "y": 32}, {"x": 1107, "y": 32}, {"x": 1107, "y": 40}, {"x": 1083, "y": 40}]}}, {"text": "und", "bounding_poly": {"vertices": [{"x": 1110, "y": 32}, {"x": 1122, "y": 32}, {"x": 1122, "y": 40}, {"x": 1110, "y": 40}]}}, {"text": "ist", "bounding_poly": {"vertices": [{"x": 1125, "y": 32}, {"x": 1133, "y": 32}, {"x": 1133, "y": 40}, {"x": 1125, "y": 40}]}}, {"text": "ohne", "bounding_poly": {"vertices": [{"x": 1135, "y": 32}, {"x": 1153, "y": 32}, {"x": 1153, "y": 40}, {"x": 1135, "y": 40}]}}, {"text": "Unterschrift", "bounding_poly": {"vertices": [{"x": 950, "y": 43}, {"x": 992, "y": 44}, {"x": 992, "y": 53}, {"x": 950, "y": 52}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 992, "y": 45}, {"x": 1013, "y": 46}, {"x": 1013, "y": 54}, {"x": 992, "y": 53}]}}]}, {"text": "Number / Nummer ***********", "bounding_poly": {"vertices": [{"x": 1186, "y": 40}, {"x": 1360, "y": 39}, {"x": 1360, "y": 50}, {"x": 1186, "y": 51}]}, "elements": [{"text": "Number", "bounding_poly": {"vertices": [{"x": 1186, "y": 40}, {"x": 1227, "y": 40}, {"x": 1227, "y": 51}, {"x": 1186, "y": 51}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1227, "y": 40}, {"x": 1231, "y": 40}, {"x": 1231, "y": 50}, {"x": 1227, "y": 50}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1231, "y": 40}, {"x": 1278, "y": 40}, {"x": 1278, "y": 50}, {"x": 1231, "y": 50}]}}, {"text": "***********", "bounding_poly": {"vertices": [{"x": 1288, "y": 40}, {"x": 1360, "y": 40}, {"x": 1360, "y": 50}, {"x": 1288, "y": 50}]}}]}, {"text": "Ausgegeben am 05/08/2024", "bounding_poly": {"vertices": [{"x": 1433, "y": 40}, {"x": 1594, "y": 38}, {"x": 1594, "y": 51}, {"x": 1433, "y": 53}]}, "elements": [{"text": "Ausgegeben", "bounding_poly": {"vertices": [{"x": 1433, "y": 40}, {"x": 1500, "y": 39}, {"x": 1500, "y": 52}, {"x": 1433, "y": 53}]}}, {"text": "am", "bounding_poly": {"vertices": [{"x": 1504, "y": 39}, {"x": 1520, "y": 39}, {"x": 1520, "y": 52}, {"x": 1504, "y": 52}]}}, {"text": "05/08/2024", "bounding_poly": {"vertices": [{"x": 1535, "y": 39}, {"x": 1594, "y": 38}, {"x": 1594, "y": 51}, {"x": 1535, "y": 52}]}}]}, {"text": "Marcegaglia", "bounding_poly": {"vertices": [{"x": 388, "y": 34}, {"x": 432, "y": 34}, {"x": 432, "y": 41}, {"x": 388, "y": 41}]}, "elements": [{"text": "Marcegaglia", "bounding_poly": {"vertices": [{"x": 388, "y": 34}, {"x": 432, "y": 34}, {"x": 432, "y": 41}, {"x": 388, "y": 41}]}}]}, {"text": "46040 <PERSON> , 16 - <PERSON><PERSON><PERSON><PERSON> degli Ippoliti Mantova - Italy Tel . + 39-0376 685 1 Fax . + 39-0376 685 600", "bounding_poly": {"vertices": [{"x": 387, "y": 45}, {"x": 600, "y": 45}, {"x": 600, "y": 64}, {"x": 387, "y": 64}]}, "elements": [{"text": "46040", "bounding_poly": {"vertices": [{"x": 387, "y": 45}, {"x": 410, "y": 45}, {"x": 410, "y": 53}, {"x": 387, "y": 53}]}}, {"text": "Via", "bounding_poly": {"vertices": [{"x": 412, "y": 45}, {"x": 424, "y": 45}, {"x": 424, "y": 53}, {"x": 412, "y": 53}]}}, {"text": "B<PERSON>ciani", "bounding_poly": {"vertices": [{"x": 426, "y": 45}, {"x": 459, "y": 45}, {"x": 459, "y": 53}, {"x": 426, "y": 53}]}}, {"text": ",", "bounding_poly": {"vertices": [{"x": 459, "y": 45}, {"x": 461, "y": 45}, {"x": 461, "y": 53}, {"x": 459, "y": 53}]}}, {"text": "16", "bounding_poly": {"vertices": [{"x": 464, "y": 45}, {"x": 472, "y": 45}, {"x": 472, "y": 53}, {"x": 464, "y": 53}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 472, "y": 45}, {"x": 476, "y": 45}, {"x": 476, "y": 53}, {"x": 472, "y": 53}]}}, {"text": "Gazoldo", "bounding_poly": {"vertices": [{"x": 475, "y": 45}, {"x": 504, "y": 45}, {"x": 504, "y": 53}, {"x": 475, "y": 53}]}}, {"text": "<PERSON>gli", "bounding_poly": {"vertices": [{"x": 507, "y": 45}, {"x": 524, "y": 45}, {"x": 524, "y": 53}, {"x": 507, "y": 53}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 526, "y": 45}, {"x": 549, "y": 45}, {"x": 549, "y": 53}, {"x": 526, "y": 53}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 552, "y": 45}, {"x": 583, "y": 45}, {"x": 583, "y": 53}, {"x": 552, "y": 53}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 582, "y": 45}, {"x": 585, "y": 45}, {"x": 585, "y": 53}, {"x": 582, "y": 53}]}}, {"text": "Italy", "bounding_poly": {"vertices": [{"x": 585, "y": 45}, {"x": 600, "y": 45}, {"x": 600, "y": 53}, {"x": 585, "y": 53}]}}, {"text": "Tel", "bounding_poly": {"vertices": [{"x": 387, "y": 57}, {"x": 400, "y": 57}, {"x": 400, "y": 64}, {"x": 387, "y": 64}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 399, "y": 57}, {"x": 402, "y": 57}, {"x": 402, "y": 64}, {"x": 399, "y": 64}]}}, {"text": "+", "bounding_poly": {"vertices": [{"x": 403, "y": 57}, {"x": 407, "y": 57}, {"x": 407, "y": 64}, {"x": 403, "y": 64}]}}, {"text": "39-0376", "bounding_poly": {"vertices": [{"x": 408, "y": 57}, {"x": 442, "y": 57}, {"x": 442, "y": 64}, {"x": 408, "y": 64}]}}, {"text": "685", "bounding_poly": {"vertices": [{"x": 444, "y": 57}, {"x": 458, "y": 57}, {"x": 458, "y": 64}, {"x": 444, "y": 64}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 460, "y": 57}, {"x": 463, "y": 57}, {"x": 463, "y": 64}, {"x": 460, "y": 64}]}}, {"text": "Fax", "bounding_poly": {"vertices": [{"x": 466, "y": 57}, {"x": 480, "y": 57}, {"x": 480, "y": 64}, {"x": 466, "y": 64}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 479, "y": 57}, {"x": 482, "y": 57}, {"x": 482, "y": 64}, {"x": 479, "y": 64}]}}, {"text": "+", "bounding_poly": {"vertices": [{"x": 483, "y": 57}, {"x": 487, "y": 57}, {"x": 487, "y": 64}, {"x": 483, "y": 64}]}}, {"text": "39-0376", "bounding_poly": {"vertices": [{"x": 488, "y": 57}, {"x": 523, "y": 57}, {"x": 523, "y": 64}, {"x": 488, "y": 64}]}}, {"text": "685", "bounding_poly": {"vertices": [{"x": 525, "y": 57}, {"x": 538, "y": 57}, {"x": 538, "y": 64}, {"x": 525, "y": 64}]}}, {"text": "600", "bounding_poly": {"vertices": [{"x": 540, "y": 57}, {"x": 554, "y": 57}, {"x": 554, "y": 64}, {"x": 540, "y": 64}]}}]}, {"text": "www.marcegaglia.com", "bounding_poly": {"vertices": [{"x": 388, "y": 68}, {"x": 467, "y": 69}, {"x": 467, "y": 77}, {"x": 388, "y": 76}]}, "elements": [{"text": "www.marcegaglia.com", "bounding_poly": {"vertices": [{"x": 388, "y": 68}, {"x": 467, "y": 69}, {"x": 467, "y": 77}, {"x": 388, "y": 76}]}}]}, {"text": "Stab . di S<PERSON> No<PERSON>o : \nvia <PERSON> 33 , 33058 <PERSON><PERSON> Nogaro - Udine tel + 390431624111 fax 0431624222", "bounding_poly": {"vertices": [{"x": 387, "y": 92}, {"x": 580, "y": 94}, {"x": 580, "y": 126}, {"x": 387, "y": 124}]}, "elements": [{"text": "Stab", "bounding_poly": {"vertices": [{"x": 387, "y": 93}, {"x": 405, "y": 93}, {"x": 405, "y": 101}, {"x": 387, "y": 101}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 404, "y": 93}, {"x": 406, "y": 93}, {"x": 406, "y": 101}, {"x": 404, "y": 101}]}}, {"text": "di", "bounding_poly": {"vertices": [{"x": 408, "y": 93}, {"x": 415, "y": 93}, {"x": 415, "y": 101}, {"x": 408, "y": 101}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 417, "y": 93}, {"x": 451, "y": 94}, {"x": 451, "y": 102}, {"x": 417, "y": 101}]}}, {"text": "di", "bounding_poly": {"vertices": [{"x": 452, "y": 94}, {"x": 459, "y": 94}, {"x": 459, "y": 102}, {"x": 452, "y": 102}]}}, {"text": "Nogaro", "bounding_poly": {"vertices": [{"x": 460, "y": 94}, {"x": 488, "y": 94}, {"x": 488, "y": 102}, {"x": 460, "y": 102}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 487, "y": 94}, {"x": 490, "y": 94}, {"x": 490, "y": 102}, {"x": 487, "y": 102}]}}, {"text": "via", "bounding_poly": {"vertices": [{"x": 387, "y": 105}, {"x": 398, "y": 105}, {"x": 398, "y": 113}, {"x": 387, "y": 113}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 400, "y": 105}, {"x": 423, "y": 105}, {"x": 423, "y": 113}, {"x": 400, "y": 113}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 425, "y": 105}, {"x": 444, "y": 105}, {"x": 444, "y": 113}, {"x": 425, "y": 113}]}}, {"text": "33", "bounding_poly": {"vertices": [{"x": 448, "y": 105}, {"x": 457, "y": 105}, {"x": 457, "y": 113}, {"x": 448, "y": 113}]}}, {"text": ",", "bounding_poly": {"vertices": [{"x": 457, "y": 105}, {"x": 459, "y": 105}, {"x": 459, "y": 113}, {"x": 457, "y": 113}]}}, {"text": "33058", "bounding_poly": {"vertices": [{"x": 461, "y": 105}, {"x": 484, "y": 105}, {"x": 484, "y": 113}, {"x": 461, "y": 113}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 485, "y": 105}, {"x": 519, "y": 105}, {"x": 519, "y": 113}, {"x": 485, "y": 113}]}}, {"text": "di", "bounding_poly": {"vertices": [{"x": 521, "y": 105}, {"x": 528, "y": 105}, {"x": 528, "y": 113}, {"x": 521, "y": 113}]}}, {"text": "Nogaro", "bounding_poly": {"vertices": [{"x": 529, "y": 105}, {"x": 556, "y": 105}, {"x": 556, "y": 113}, {"x": 529, "y": 113}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 556, "y": 105}, {"x": 560, "y": 105}, {"x": 560, "y": 113}, {"x": 556, "y": 113}]}}, {"text": "Udine", "bounding_poly": {"vertices": [{"x": 559, "y": 105}, {"x": 580, "y": 105}, {"x": 580, "y": 113}, {"x": 559, "y": 113}]}}, {"text": "tel", "bounding_poly": {"vertices": [{"x": 387, "y": 117}, {"x": 397, "y": 117}, {"x": 397, "y": 124}, {"x": 387, "y": 124}]}}, {"text": "+", "bounding_poly": {"vertices": [{"x": 397, "y": 117}, {"x": 401, "y": 117}, {"x": 401, "y": 124}, {"x": 397, "y": 124}]}}, {"text": "390431624111", "bounding_poly": {"vertices": [{"x": 401, "y": 117}, {"x": 453, "y": 117}, {"x": 453, "y": 124}, {"x": 401, "y": 124}]}}, {"text": "fax", "bounding_poly": {"vertices": [{"x": 456, "y": 117}, {"x": 468, "y": 117}, {"x": 468, "y": 124}, {"x": 456, "y": 124}]}}, {"text": "0431624222", "bounding_poly": {"vertices": [{"x": 469, "y": 117}, {"x": 514, "y": 117}, {"x": 514, "y": 124}, {"x": 469, "y": 124}]}}]}, {"text": "Type / Typ", "bounding_poly": {"vertices": [{"x": 616, "y": 41}, {"x": 665, "y": 41}, {"x": 665, "y": 52}, {"x": 616, "y": 52}]}, "elements": [{"text": "Type", "bounding_poly": {"vertices": [{"x": 616, "y": 41}, {"x": 641, "y": 41}, {"x": 641, "y": 52}, {"x": 616, "y": 52}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 642, "y": 41}, {"x": 647, "y": 41}, {"x": 647, "y": 52}, {"x": 642, "y": 52}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 646, "y": 41}, {"x": 665, "y": 41}, {"x": 665, "y": 52}, {"x": 646, "y": 52}]}}]}, {"text": "Abnahmeprüfzeugnis 3.1 EN 10204 Product : Hot Rolled Heavy Plates \nNormaCollaudo Lamiera Treno : EN10025-2 : 2019", "bounding_poly": {"vertices": [{"x": 616, "y": 40}, {"x": 956, "y": 40}, {"x": 956, "y": 107}, {"x": 616, "y": 107}]}, "elements": [{"text": "Abnahmeprüfzeugnis", "bounding_poly": {"vertices": [{"x": 699, "y": 41}, {"x": 812, "y": 40}, {"x": 812, "y": 52}, {"x": 699, "y": 53}]}}, {"text": "3.1", "bounding_poly": {"vertices": [{"x": 816, "y": 40}, {"x": 830, "y": 40}, {"x": 830, "y": 52}, {"x": 816, "y": 52}]}}, {"text": "EN", "bounding_poly": {"vertices": [{"x": 836, "y": 40}, {"x": 852, "y": 40}, {"x": 852, "y": 51}, {"x": 836, "y": 51}]}}, {"text": "10204", "bounding_poly": {"vertices": [{"x": 856, "y": 40}, {"x": 889, "y": 40}, {"x": 889, "y": 51}, {"x": 856, "y": 51}]}}, {"text": "Product", "bounding_poly": {"vertices": [{"x": 616, "y": 69}, {"x": 671, "y": 69}, {"x": 671, "y": 84}, {"x": 616, "y": 84}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 672, "y": 69}, {"x": 676, "y": 69}, {"x": 676, "y": 84}, {"x": 672, "y": 84}]}}, {"text": "Hot", "bounding_poly": {"vertices": [{"x": 681, "y": 69}, {"x": 705, "y": 69}, {"x": 705, "y": 84}, {"x": 681, "y": 84}]}}, {"text": "Rolled", "bounding_poly": {"vertices": [{"x": 710, "y": 69}, {"x": 753, "y": 69}, {"x": 753, "y": 84}, {"x": 710, "y": 84}]}}, {"text": "Heavy", "bounding_poly": {"vertices": [{"x": 760, "y": 69}, {"x": 804, "y": 69}, {"x": 804, "y": 84}, {"x": 760, "y": 84}]}}, {"text": "Plates", "bounding_poly": {"vertices": [{"x": 810, "y": 69}, {"x": 853, "y": 69}, {"x": 853, "y": 84}, {"x": 810, "y": 84}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 616, "y": 94}, {"x": 727, "y": 94}, {"x": 727, "y": 107}, {"x": 616, "y": 107}]}}, {"text": "Lamiera", "bounding_poly": {"vertices": [{"x": 728, "y": 94}, {"x": 785, "y": 94}, {"x": 785, "y": 107}, {"x": 728, "y": 107}]}}, {"text": "Treno", "bounding_poly": {"vertices": [{"x": 785, "y": 94}, {"x": 827, "y": 94}, {"x": 827, "y": 107}, {"x": 785, "y": 107}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 827, "y": 94}, {"x": 831, "y": 94}, {"x": 831, "y": 107}, {"x": 827, "y": 107}]}}, {"text": "EN10025-2", "bounding_poly": {"vertices": [{"x": 836, "y": 94}, {"x": 917, "y": 94}, {"x": 917, "y": 107}, {"x": 836, "y": 107}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 917, "y": 94}, {"x": 921, "y": 94}, {"x": 921, "y": 107}, {"x": 917, "y": 107}]}}, {"text": "2019", "bounding_poly": {"vertices": [{"x": 921, "y": 94}, {"x": 956, "y": 94}, {"x": 956, "y": 107}, {"x": 921, "y": 107}]}}]}, {"text": "Steel grade :: S355J2 + N", "bounding_poly": {"vertices": [{"x": 615, "y": 117}, {"x": 785, "y": 116}, {"x": 785, "y": 132}, {"x": 615, "y": 133}]}, "elements": [{"text": "Steel", "bounding_poly": {"vertices": [{"x": 615, "y": 117}, {"x": 651, "y": 117}, {"x": 651, "y": 133}, {"x": 615, "y": 133}]}}, {"text": "grade", "bounding_poly": {"vertices": [{"x": 656, "y": 117}, {"x": 696, "y": 117}, {"x": 696, "y": 132}, {"x": 656, "y": 132}]}}, {"text": "::", "bounding_poly": {"vertices": [{"x": 697, "y": 117}, {"x": 706, "y": 117}, {"x": 706, "y": 132}, {"x": 697, "y": 132}]}}, {"text": "S355J2", "bounding_poly": {"vertices": [{"x": 710, "y": 117}, {"x": 765, "y": 117}, {"x": 765, "y": 132}, {"x": 710, "y": 132}]}}, {"text": "+", "bounding_poly": {"vertices": [{"x": 764, "y": 117}, {"x": 773, "y": 117}, {"x": 773, "y": 132}, {"x": 764, "y": 132}]}}, {"text": "N", "bounding_poly": {"vertices": [{"x": 774, "y": 117}, {"x": 785, "y": 117}, {"x": 785, "y": 132}, {"x": 774, "y": 132}]}}]}, {"text": "Code : 0000036345", "bounding_poly": {"vertices": [{"x": 474, "y": 145}, {"x": 575, "y": 145}, {"x": 575, "y": 155}, {"x": 474, "y": 155}]}, "elements": [{"text": "Code", "bounding_poly": {"vertices": [{"x": 474, "y": 145}, {"x": 502, "y": 145}, {"x": 502, "y": 155}, {"x": 474, "y": 155}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 503, "y": 145}, {"x": 507, "y": 145}, {"x": 507, "y": 155}, {"x": 503, "y": 155}]}}, {"text": "0000036345", "bounding_poly": {"vertices": [{"x": 509, "y": 145}, {"x": 575, "y": 145}, {"x": 575, "y": 155}, {"x": 509, "y": 155}]}}]}, {"text": "Del . Address / Anlieferadresse", "bounding_poly": {"vertices": [{"x": 622, "y": 149}, {"x": 778, "y": 149}, {"x": 778, "y": 159}, {"x": 622, "y": 159}]}, "elements": [{"text": "Del", "bounding_poly": {"vertices": [{"x": 622, "y": 149}, {"x": 640, "y": 149}, {"x": 640, "y": 159}, {"x": 622, "y": 159}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 640, "y": 149}, {"x": 643, "y": 149}, {"x": 643, "y": 159}, {"x": 640, "y": 159}]}}, {"text": "Address", "bounding_poly": {"vertices": [{"x": 646, "y": 149}, {"x": 690, "y": 149}, {"x": 690, "y": 159}, {"x": 646, "y": 159}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 690, "y": 149}, {"x": 695, "y": 149}, {"x": 695, "y": 159}, {"x": 690, "y": 159}]}}, {"text": "Anlieferadresse", "bounding_poly": {"vertices": [{"x": 694, "y": 149}, {"x": 778, "y": 149}, {"x": 778, "y": 159}, {"x": 694, "y": 159}]}}]}, {"text": "Delivery / Lieferung nr", "bounding_poly": {"vertices": [{"x": 979, "y": 149}, {"x": 1089, "y": 149}, {"x": 1089, "y": 160}, {"x": 979, "y": 160}]}, "elements": [{"text": "Delivery", "bounding_poly": {"vertices": [{"x": 979, "y": 149}, {"x": 1021, "y": 149}, {"x": 1021, "y": 160}, {"x": 979, "y": 160}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1021, "y": 149}, {"x": 1026, "y": 149}, {"x": 1026, "y": 160}, {"x": 1021, "y": 160}]}}, {"text": "Lieferung", "bounding_poly": {"vertices": [{"x": 1025, "y": 149}, {"x": 1075, "y": 149}, {"x": 1075, "y": 160}, {"x": 1025, "y": 160}]}}, {"text": "nr", "bounding_poly": {"vertices": [{"x": 1078, "y": 149}, {"x": 1089, "y": 149}, {"x": 1089, "y": 160}, {"x": 1078, "y": 160}]}}]}, {"text": "8930483286", "bounding_poly": {"vertices": [{"x": 1190, "y": 147}, {"x": 1256, "y": 148}, {"x": 1256, "y": 158}, {"x": 1190, "y": 157}]}, "elements": [{"text": "8930483286", "bounding_poly": {"vertices": [{"x": 1190, "y": 147}, {"x": 1256, "y": 148}, {"x": 1256, "y": 158}, {"x": 1190, "y": 157}]}}]}, {"text": "JEBENS GMBH", "bounding_poly": {"vertices": [{"x": 622, "y": 164}, {"x": 736, "y": 164}, {"x": 736, "y": 177}, {"x": 622, "y": 177}]}, "elements": [{"text": "JEBENS", "bounding_poly": {"vertices": [{"x": 622, "y": 164}, {"x": 684, "y": 164}, {"x": 684, "y": 177}, {"x": 622, "y": 177}]}}, {"text": "GMBH", "bounding_poly": {"vertices": [{"x": 689, "y": 164}, {"x": 736, "y": 164}, {"x": 736, "y": 177}, {"x": 689, "y": 177}]}}]}, {"text": "Of / Vom", "bounding_poly": {"vertices": [{"x": 978, "y": 175}, {"x": 1018, "y": 175}, {"x": 1018, "y": 184}, {"x": 978, "y": 184}]}, "elements": [{"text": "Of", "bounding_poly": {"vertices": [{"x": 978, "y": 175}, {"x": 992, "y": 175}, {"x": 992, "y": 184}, {"x": 978, "y": 184}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 990, "y": 175}, {"x": 995, "y": 175}, {"x": 995, "y": 184}, {"x": 990, "y": 184}]}}, {"text": "Vom", "bounding_poly": {"vertices": [{"x": 994, "y": 175}, {"x": 1018, "y": 175}, {"x": 1018, "y": 184}, {"x": 994, "y": 184}]}}]}, {"text": "05/08/2024", "bounding_poly": {"vertices": [{"x": 1189, "y": 175}, {"x": 1250, "y": 175}, {"x": 1250, "y": 183}, {"x": 1189, "y": 183}]}, "elements": [{"text": "05/08/2024", "bounding_poly": {"vertices": [{"x": 1189, "y": 175}, {"x": 1250, "y": 175}, {"x": 1250, "y": 183}, {"x": 1189, "y": 183}]}}]}, {"text": "DAIMLERSTR.35-37", "bounding_poly": {"vertices": [{"x": 622, "y": 182}, {"x": 769, "y": 182}, {"x": 769, "y": 194}, {"x": 622, "y": 194}]}, "elements": [{"text": "DAIMLERSTR.35-37", "bounding_poly": {"vertices": [{"x": 622, "y": 182}, {"x": 769, "y": 182}, {"x": 769, "y": 194}, {"x": 622, "y": 194}]}}]}, {"text": "70825 KORNTAL - MUENCHINGEN DE", "bounding_poly": {"vertices": [{"x": 622, "y": 200}, {"x": 898, "y": 200}, {"x": 898, "y": 212}, {"x": 622, "y": 212}]}, "elements": [{"text": "70825", "bounding_poly": {"vertices": [{"x": 622, "y": 200}, {"x": 665, "y": 200}, {"x": 665, "y": 212}, {"x": 622, "y": 212}]}}, {"text": "KORNTAL", "bounding_poly": {"vertices": [{"x": 671, "y": 200}, {"x": 746, "y": 200}, {"x": 746, "y": 212}, {"x": 671, "y": 212}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 746, "y": 200}, {"x": 752, "y": 200}, {"x": 752, "y": 212}, {"x": 746, "y": 212}]}}, {"text": "MUENCHINGEN", "bounding_poly": {"vertices": [{"x": 752, "y": 200}, {"x": 871, "y": 200}, {"x": 871, "y": 212}, {"x": 752, "y": 212}]}}, {"text": "DE", "bounding_poly": {"vertices": [{"x": 878, "y": 200}, {"x": 898, "y": 200}, {"x": 898, "y": 212}, {"x": 878, "y": 212}]}}]}, {"text": "Delivery note nr / Lieferschein nr", "bounding_poly": {"vertices": [{"x": 978, "y": 200}, {"x": 1144, "y": 199}, {"x": 1144, "y": 212}, {"x": 978, "y": 213}]}, "elements": [{"text": "Delivery", "bounding_poly": {"vertices": [{"x": 978, "y": 200}, {"x": 1020, "y": 200}, {"x": 1020, "y": 213}, {"x": 978, "y": 213}]}}, {"text": "note", "bounding_poly": {"vertices": [{"x": 1024, "y": 200}, {"x": 1046, "y": 200}, {"x": 1046, "y": 212}, {"x": 1024, "y": 212}]}}, {"text": "nr", "bounding_poly": {"vertices": [{"x": 1051, "y": 200}, {"x": 1061, "y": 200}, {"x": 1061, "y": 212}, {"x": 1051, "y": 212}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1060, "y": 200}, {"x": 1064, "y": 200}, {"x": 1064, "y": 212}, {"x": 1060, "y": 212}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1064, "y": 199}, {"x": 1128, "y": 198}, {"x": 1128, "y": 211}, {"x": 1064, "y": 212}]}}, {"text": "nr", "bounding_poly": {"vertices": [{"x": 1132, "y": 199}, {"x": 1144, "y": 199}, {"x": 1144, "y": 211}, {"x": 1132, "y": 211}]}}]}, {"text": "1060009915", "bounding_poly": {"vertices": [{"x": 1190, "y": 201}, {"x": 1256, "y": 201}, {"x": 1256, "y": 210}, {"x": 1190, "y": 210}]}, "elements": [{"text": "1060009915", "bounding_poly": {"vertices": [{"x": 1190, "y": 201}, {"x": 1256, "y": 201}, {"x": 1256, "y": 210}, {"x": 1190, "y": 210}]}}]}, {"text": "Surface Quality : EN10163-2 CL A1", "bounding_poly": {"vertices": [{"x": 484, "y": 234}, {"x": 730, "y": 234}, {"x": 730, "y": 248}, {"x": 484, "y": 248}]}, "elements": [{"text": "Surface", "bounding_poly": {"vertices": [{"x": 484, "y": 234}, {"x": 538, "y": 234}, {"x": 538, "y": 248}, {"x": 484, "y": 248}]}}, {"text": "Quality", "bounding_poly": {"vertices": [{"x": 543, "y": 234}, {"x": 594, "y": 234}, {"x": 594, "y": 248}, {"x": 543, "y": 248}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 594, "y": 234}, {"x": 598, "y": 234}, {"x": 598, "y": 248}, {"x": 594, "y": 248}]}}, {"text": "EN10163-2", "bounding_poly": {"vertices": [{"x": 602, "y": 234}, {"x": 683, "y": 234}, {"x": 683, "y": 248}, {"x": 602, "y": 248}]}}, {"text": "CL", "bounding_poly": {"vertices": [{"x": 688, "y": 234}, {"x": 708, "y": 234}, {"x": 708, "y": 248}, {"x": 688, "y": 248}]}}, {"text": "A1", "bounding_poly": {"vertices": [{"x": 711, "y": 234}, {"x": 730, "y": 234}, {"x": 730, "y": 248}, {"x": 711, "y": 248}]}}]}, {"text": "Flatness : rif EN 10029 cl . N Toleranzen : EN10029 CLASSE B", "bounding_poly": {"vertices": [{"x": 484, "y": 258}, {"x": 723, "y": 258}, {"x": 723, "y": 295}, {"x": 484, "y": 295}]}, "elements": [{"text": "Flatness", "bounding_poly": {"vertices": [{"x": 485, "y": 258}, {"x": 544, "y": 258}, {"x": 544, "y": 271}, {"x": 485, "y": 271}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 545, "y": 258}, {"x": 549, "y": 258}, {"x": 549, "y": 271}, {"x": 545, "y": 271}]}}, {"text": "rif", "bounding_poly": {"vertices": [{"x": 553, "y": 258}, {"x": 567, "y": 258}, {"x": 567, "y": 271}, {"x": 553, "y": 271}]}}, {"text": "EN", "bounding_poly": {"vertices": [{"x": 572, "y": 258}, {"x": 592, "y": 258}, {"x": 592, "y": 271}, {"x": 572, "y": 271}]}}, {"text": "10029", "bounding_poly": {"vertices": [{"x": 598, "y": 258}, {"x": 642, "y": 258}, {"x": 642, "y": 271}, {"x": 598, "y": 271}]}}, {"text": "cl", "bounding_poly": {"vertices": [{"x": 646, "y": 258}, {"x": 658, "y": 258}, {"x": 658, "y": 271}, {"x": 646, "y": 271}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 658, "y": 258}, {"x": 663, "y": 258}, {"x": 663, "y": 271}, {"x": 658, "y": 271}]}}, {"text": "N", "bounding_poly": {"vertices": [{"x": 667, "y": 258}, {"x": 678, "y": 258}, {"x": 678, "y": 271}, {"x": 667, "y": 271}]}}, {"text": "Toleranzen", "bounding_poly": {"vertices": [{"x": 484, "y": 282}, {"x": 564, "y": 282}, {"x": 564, "y": 295}, {"x": 484, "y": 295}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 564, "y": 282}, {"x": 568, "y": 282}, {"x": 568, "y": 295}, {"x": 564, "y": 295}]}}, {"text": "EN10029", "bounding_poly": {"vertices": [{"x": 574, "y": 282}, {"x": 639, "y": 282}, {"x": 639, "y": 295}, {"x": 574, "y": 295}]}}, {"text": "CLASSE", "bounding_poly": {"vertices": [{"x": 644, "y": 282}, {"x": 707, "y": 282}, {"x": 707, "y": 295}, {"x": 644, "y": 295}]}}, {"text": "B", "bounding_poly": {"vertices": [{"x": 712, "y": 282}, {"x": 723, "y": 282}, {"x": 723, "y": 295}, {"x": 712, "y": 295}]}}]}, {"text": "Delivery Condition : NORMALIZING ROLLING Steel Making Process : BO = Basic oxigen", "bounding_poly": {"vertices": [{"x": 1052, "y": 230}, {"x": 1293, "y": 230}, {"x": 1293, "y": 260}, {"x": 1052, "y": 260}]}, "elements": [{"text": "Delivery", "bounding_poly": {"vertices": [{"x": 1052, "y": 230}, {"x": 1094, "y": 230}, {"x": 1094, "y": 241}, {"x": 1052, "y": 241}]}}, {"text": "Condition", "bounding_poly": {"vertices": [{"x": 1098, "y": 230}, {"x": 1148, "y": 230}, {"x": 1148, "y": 240}, {"x": 1098, "y": 240}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1149, "y": 230}, {"x": 1152, "y": 230}, {"x": 1152, "y": 240}, {"x": 1149, "y": 240}]}}, {"text": "NORMALIZING", "bounding_poly": {"vertices": [{"x": 1155, "y": 230}, {"x": 1237, "y": 230}, {"x": 1237, "y": 240}, {"x": 1155, "y": 240}]}}, {"text": "ROLLING", "bounding_poly": {"vertices": [{"x": 1242, "y": 230}, {"x": 1293, "y": 230}, {"x": 1293, "y": 240}, {"x": 1242, "y": 240}]}}, {"text": "Steel", "bounding_poly": {"vertices": [{"x": 1052, "y": 249}, {"x": 1078, "y": 249}, {"x": 1078, "y": 260}, {"x": 1052, "y": 260}]}}, {"text": "Making", "bounding_poly": {"vertices": [{"x": 1083, "y": 249}, {"x": 1121, "y": 249}, {"x": 1121, "y": 260}, {"x": 1083, "y": 260}]}}, {"text": "Process", "bounding_poly": {"vertices": [{"x": 1125, "y": 249}, {"x": 1168, "y": 249}, {"x": 1168, "y": 260}, {"x": 1125, "y": 260}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1168, "y": 249}, {"x": 1172, "y": 249}, {"x": 1172, "y": 260}, {"x": 1168, "y": 260}]}}, {"text": "BO", "bounding_poly": {"vertices": [{"x": 1175, "y": 249}, {"x": 1192, "y": 249}, {"x": 1192, "y": 260}, {"x": 1175, "y": 260}]}}, {"text": "=", "bounding_poly": {"vertices": [{"x": 1191, "y": 249}, {"x": 1199, "y": 249}, {"x": 1199, "y": 260}, {"x": 1191, "y": 260}]}}, {"text": "Basic", "bounding_poly": {"vertices": [{"x": 1202, "y": 249}, {"x": 1232, "y": 249}, {"x": 1232, "y": 260}, {"x": 1202, "y": 260}]}}, {"text": "oxigen", "bounding_poly": {"vertices": [{"x": 1235, "y": 249}, {"x": 1270, "y": 249}, {"x": 1270, "y": 260}, {"x": 1235, "y": 260}]}}]}, {"text": "Quality Control / Qualitaetsicherung Q.M.D./Bearb . M.Quargnali Plant / Werk S. Giorgio di Nogaro \nOrder Nr / Auftrag Nr 1191789210/40", "bounding_poly": {"vertices": [{"x": 1305, "y": 148}, {"x": 1489, "y": 149}, {"x": 1489, "y": 251}, {"x": 1305, "y": 250}]}, "elements": [{"text": "Quality", "bounding_poly": {"vertices": [{"x": 1305, "y": 149}, {"x": 1343, "y": 149}, {"x": 1343, "y": 160}, {"x": 1305, "y": 160}]}}, {"text": "Control", "bounding_poly": {"vertices": [{"x": 1346, "y": 149}, {"x": 1384, "y": 149}, {"x": 1384, "y": 160}, {"x": 1346, "y": 160}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1385, "y": 149}, {"x": 1389, "y": 149}, {"x": 1389, "y": 160}, {"x": 1385, "y": 160}]}}, {"text": "Qualitaetsicherung", "bounding_poly": {"vertices": [{"x": 1388, "y": 149}, {"x": 1489, "y": 149}, {"x": 1489, "y": 160}, {"x": 1388, "y": 160}]}}, {"text": "Q.M.D./Bearb", "bounding_poly": {"vertices": [{"x": 1306, "y": 172}, {"x": 1379, "y": 173}, {"x": 1379, "y": 185}, {"x": 1306, "y": 184}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 1379, "y": 173}, {"x": 1382, "y": 173}, {"x": 1382, "y": 184}, {"x": 1379, "y": 184}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1386, "y": 173}, {"x": 1452, "y": 174}, {"x": 1452, "y": 186}, {"x": 1386, "y": 185}]}}, {"text": "Plant", "bounding_poly": {"vertices": [{"x": 1306, "y": 200}, {"x": 1333, "y": 200}, {"x": 1333, "y": 211}, {"x": 1306, "y": 211}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1332, "y": 200}, {"x": 1337, "y": 200}, {"x": 1337, "y": 211}, {"x": 1332, "y": 211}]}}, {"text": "Werk", "bounding_poly": {"vertices": [{"x": 1336, "y": 200}, {"x": 1364, "y": 200}, {"x": 1364, "y": 211}, {"x": 1336, "y": 211}]}}, {"text": "S.", "bounding_poly": {"vertices": [{"x": 1368, "y": 200}, {"x": 1379, "y": 200}, {"x": 1379, "y": 211}, {"x": 1368, "y": 211}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1383, "y": 200}, {"x": 1422, "y": 200}, {"x": 1422, "y": 212}, {"x": 1383, "y": 212}]}}, {"text": "di", "bounding_poly": {"vertices": [{"x": 1424, "y": 201}, {"x": 1434, "y": 201}, {"x": 1434, "y": 212}, {"x": 1424, "y": 212}]}}, {"text": "Nogaro", "bounding_poly": {"vertices": [{"x": 1437, "y": 201}, {"x": 1477, "y": 201}, {"x": 1477, "y": 213}, {"x": 1437, "y": 213}]}}, {"text": "Order", "bounding_poly": {"vertices": [{"x": 1357, "y": 224}, {"x": 1387, "y": 224}, {"x": 1387, "y": 235}, {"x": 1357, "y": 235}]}}, {"text": "Nr", "bounding_poly": {"vertices": [{"x": 1391, "y": 224}, {"x": 1404, "y": 224}, {"x": 1404, "y": 234}, {"x": 1391, "y": 234}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1404, "y": 224}, {"x": 1409, "y": 224}, {"x": 1409, "y": 234}, {"x": 1404, "y": 234}]}}, {"text": "Auftrag", "bounding_poly": {"vertices": [{"x": 1407, "y": 224}, {"x": 1445, "y": 224}, {"x": 1445, "y": 234}, {"x": 1407, "y": 234}]}}, {"text": "Nr", "bounding_poly": {"vertices": [{"x": 1449, "y": 224}, {"x": 1463, "y": 224}, {"x": 1463, "y": 234}, {"x": 1449, "y": 234}]}}, {"text": "1191789210/40", "bounding_poly": {"vertices": [{"x": 1358, "y": 238}, {"x": 1469, "y": 238}, {"x": 1469, "y": 250}, {"x": 1358, "y": 250}]}}]}, {"text": "Part Number", "bounding_poly": {"vertices": [{"x": 1358, "y": 264}, {"x": 1426, "y": 265}, {"x": 1426, "y": 276}, {"x": 1358, "y": 275}]}, "elements": [{"text": "Part", "bounding_poly": {"vertices": [{"x": 1358, "y": 264}, {"x": 1380, "y": 264}, {"x": 1380, "y": 275}, {"x": 1358, "y": 275}]}}, {"text": "Number", "bounding_poly": {"vertices": [{"x": 1382, "y": 264}, {"x": 1426, "y": 265}, {"x": 1426, "y": 276}, {"x": 1382, "y": 275}]}}]}, {"text": "Pages / Seite", "bounding_poly": {"vertices": [{"x": 1562, "y": 148}, {"x": 1625, "y": 148}, {"x": 1625, "y": 160}, {"x": 1562, "y": 160}]}, "elements": [{"text": "Pages", "bounding_poly": {"vertices": [{"x": 1562, "y": 148}, {"x": 1594, "y": 148}, {"x": 1594, "y": 160}, {"x": 1562, "y": 160}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1594, "y": 148}, {"x": 1598, "y": 148}, {"x": 1598, "y": 159}, {"x": 1594, "y": 159}]}}, {"text": "Seite", "bounding_poly": {"vertices": [{"x": 1598, "y": 148}, {"x": 1625, "y": 148}, {"x": 1625, "y": 159}, {"x": 1598, "y": 159}]}}]}, {"text": "1/2", "bounding_poly": {"vertices": [{"x": 1592, "y": 179}, {"x": 1614, "y": 179}, {"x": 1614, "y": 189}, {"x": 1592, "y": 189}]}, "elements": [{"text": "1/2", "bounding_poly": {"vertices": [{"x": 1592, "y": 179}, {"x": 1614, "y": 179}, {"x": 1614, "y": 189}, {"x": 1592, "y": 189}]}}]}, {"text": "Client Order / Kundenauftrag 61039475", "bounding_poly": {"vertices": [{"x": 1500, "y": 225}, {"x": 1646, "y": 225}, {"x": 1646, "y": 250}, {"x": 1500, "y": 250}]}, "elements": [{"text": "Client", "bounding_poly": {"vertices": [{"x": 1500, "y": 225}, {"x": 1530, "y": 225}, {"x": 1530, "y": 235}, {"x": 1500, "y": 235}]}}, {"text": "Order", "bounding_poly": {"vertices": [{"x": 1533, "y": 225}, {"x": 1565, "y": 225}, {"x": 1565, "y": 235}, {"x": 1533, "y": 235}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1564, "y": 225}, {"x": 1568, "y": 225}, {"x": 1568, "y": 235}, {"x": 1564, "y": 235}]}}, {"text": "Kundenauftrag", "bounding_poly": {"vertices": [{"x": 1567, "y": 225}, {"x": 1646, "y": 225}, {"x": 1646, "y": 235}, {"x": 1567, "y": 235}]}}, {"text": "61039475", "bounding_poly": {"vertices": [{"x": 1500, "y": 238}, {"x": 1569, "y": 238}, {"x": 1569, "y": 250}, {"x": 1500, "y": 250}]}}]}, {"text": "Client Date / Kundendatum 18/07/2024", "bounding_poly": {"vertices": [{"x": 1500, "y": 265}, {"x": 1635, "y": 265}, {"x": 1635, "y": 290}, {"x": 1500, "y": 290}]}, "elements": [{"text": "Client", "bounding_poly": {"vertices": [{"x": 1500, "y": 265}, {"x": 1531, "y": 265}, {"x": 1531, "y": 274}, {"x": 1500, "y": 274}]}}, {"text": "Date", "bounding_poly": {"vertices": [{"x": 1534, "y": 265}, {"x": 1558, "y": 265}, {"x": 1558, "y": 274}, {"x": 1534, "y": 274}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1559, "y": 265}, {"x": 1563, "y": 265}, {"x": 1563, "y": 274}, {"x": 1559, "y": 274}]}}, {"text": "Kundendatum", "bounding_poly": {"vertices": [{"x": 1563, "y": 265}, {"x": 1635, "y": 265}, {"x": 1635, "y": 274}, {"x": 1563, "y": 274}]}}, {"text": "18/07/2024", "bounding_poly": {"vertices": [{"x": 1500, "y": 278}, {"x": 1580, "y": 278}, {"x": 1580, "y": 290}, {"x": 1500, "y": 290}]}}]}, {"text": "Quantity / Anzahl KG", "bounding_poly": {"vertices": [{"x": 414, "y": 312}, {"x": 518, "y": 311}, {"x": 518, "y": 324}, {"x": 414, "y": 325}]}, "elements": [{"text": "Quantity", "bounding_poly": {"vertices": [{"x": 414, "y": 312}, {"x": 458, "y": 312}, {"x": 458, "y": 325}, {"x": 414, "y": 325}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 457, "y": 312}, {"x": 461, "y": 312}, {"x": 461, "y": 324}, {"x": 457, "y": 324}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 461, "y": 312}, {"x": 497, "y": 312}, {"x": 497, "y": 324}, {"x": 461, "y": 324}]}}, {"text": "KG", "bounding_poly": {"vertices": [{"x": 502, "y": 312}, {"x": 518, "y": 312}, {"x": 518, "y": 324}, {"x": 502, "y": 324}]}}]}, {"text": "Quantity / Anzahl PZZ", "bounding_poly": {"vertices": [{"x": 572, "y": 313}, {"x": 683, "y": 313}, {"x": 683, "y": 323}, {"x": 572, "y": 323}]}, "elements": [{"text": "Quantity", "bounding_poly": {"vertices": [{"x": 572, "y": 313}, {"x": 617, "y": 313}, {"x": 617, "y": 323}, {"x": 572, "y": 323}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 617, "y": 313}, {"x": 622, "y": 313}, {"x": 622, "y": 323}, {"x": 617, "y": 323}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 620, "y": 313}, {"x": 656, "y": 313}, {"x": 656, "y": 323}, {"x": 620, "y": 323}]}}, {"text": "PZZ", "bounding_poly": {"vertices": [{"x": 660, "y": 313}, {"x": 683, "y": 313}, {"x": 683, "y": 323}, {"x": 660, "y": 323}]}}]}, {"text": "4704", "bounding_poly": {"vertices": [{"x": 413, "y": 335}, {"x": 449, "y": 335}, {"x": 449, "y": 345}, {"x": 413, "y": 345}]}, "elements": [{"text": "4704", "bounding_poly": {"vertices": [{"x": 413, "y": 335}, {"x": 449, "y": 335}, {"x": 449, "y": 345}, {"x": 413, "y": 345}]}}]}, {"text": "4704", "bounding_poly": {"vertices": [{"x": 413, "y": 359}, {"x": 449, "y": 359}, {"x": 449, "y": 369}, {"x": 413, "y": 369}]}, "elements": [{"text": "4704", "bounding_poly": {"vertices": [{"x": 413, "y": 359}, {"x": 449, "y": 359}, {"x": 449, "y": 369}, {"x": 413, "y": 369}]}}]}, {"text": "1 1", "bounding_poly": {"vertices": [{"x": 572, "y": 335}, {"x": 579, "y": 335}, {"x": 580, "y": 370}, {"x": 573, "y": 370}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 572, "y": 335}, {"x": 579, "y": 335}, {"x": 579, "y": 345}, {"x": 572, "y": 345}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 572, "y": 359}, {"x": 578, "y": 359}, {"x": 578, "y": 370}, {"x": 572, "y": 370}]}}]}, {"text": "Remarks / Bemerkungen :", "bounding_poly": {"vertices": [{"x": 736, "y": 318}, {"x": 865, "y": 319}, {"x": 865, "y": 329}, {"x": 736, "y": 328}]}, "elements": [{"text": "Remarks", "bounding_poly": {"vertices": [{"x": 736, "y": 319}, {"x": 784, "y": 320}, {"x": 784, "y": 329}, {"x": 736, "y": 328}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 783, "y": 319}, {"x": 787, "y": 319}, {"x": 787, "y": 328}, {"x": 783, "y": 328}]}}, {"text": "Bemerkungen", "bounding_poly": {"vertices": [{"x": 787, "y": 319}, {"x": 861, "y": 320}, {"x": 861, "y": 330}, {"x": 787, "y": 329}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 862, "y": 320}, {"x": 865, "y": 320}, {"x": 865, "y": 329}, {"x": 862, "y": 329}]}}]}, {"text": "The Declaration of Performance ( DOP ) can be downloaded from the following web page : www.quality.marcegaglia.com/marcegaglia-plates-dop", "bounding_poly": {"vertices": [{"x": 743, "y": 337}, {"x": 1214, "y": 337}, {"x": 1214, "y": 367}, {"x": 743, "y": 367}]}, "elements": [{"text": "The", "bounding_poly": {"vertices": [{"x": 743, "y": 337}, {"x": 764, "y": 337}, {"x": 764, "y": 349}, {"x": 743, "y": 349}]}}, {"text": "Declaration", "bounding_poly": {"vertices": [{"x": 768, "y": 337}, {"x": 828, "y": 337}, {"x": 828, "y": 349}, {"x": 768, "y": 349}]}}, {"text": "of", "bounding_poly": {"vertices": [{"x": 831, "y": 337}, {"x": 844, "y": 337}, {"x": 844, "y": 349}, {"x": 831, "y": 349}]}}, {"text": "Performance", "bounding_poly": {"vertices": [{"x": 846, "y": 337}, {"x": 914, "y": 337}, {"x": 914, "y": 349}, {"x": 846, "y": 349}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 918, "y": 337}, {"x": 922, "y": 337}, {"x": 922, "y": 349}, {"x": 918, "y": 349}]}}, {"text": "DOP", "bounding_poly": {"vertices": [{"x": 922, "y": 337}, {"x": 947, "y": 337}, {"x": 947, "y": 349}, {"x": 922, "y": 349}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 946, "y": 337}, {"x": 951, "y": 337}, {"x": 951, "y": 349}, {"x": 946, "y": 349}]}}, {"text": "can", "bounding_poly": {"vertices": [{"x": 954, "y": 337}, {"x": 973, "y": 337}, {"x": 973, "y": 349}, {"x": 954, "y": 349}]}}, {"text": "be", "bounding_poly": {"vertices": [{"x": 977, "y": 337}, {"x": 990, "y": 337}, {"x": 990, "y": 349}, {"x": 977, "y": 349}]}}, {"text": "downloaded", "bounding_poly": {"vertices": [{"x": 994, "y": 337}, {"x": 1058, "y": 337}, {"x": 1058, "y": 349}, {"x": 994, "y": 349}]}}, {"text": "from", "bounding_poly": {"vertices": [{"x": 1061, "y": 337}, {"x": 1086, "y": 337}, {"x": 1086, "y": 349}, {"x": 1061, "y": 349}]}}, {"text": "the", "bounding_poly": {"vertices": [{"x": 1088, "y": 337}, {"x": 1106, "y": 337}, {"x": 1106, "y": 349}, {"x": 1088, "y": 349}]}}, {"text": "following", "bounding_poly": {"vertices": [{"x": 1109, "y": 337}, {"x": 1155, "y": 337}, {"x": 1155, "y": 349}, {"x": 1109, "y": 349}]}}, {"text": "web", "bounding_poly": {"vertices": [{"x": 1158, "y": 337}, {"x": 1181, "y": 337}, {"x": 1181, "y": 349}, {"x": 1158, "y": 349}]}}, {"text": "page", "bounding_poly": {"vertices": [{"x": 1184, "y": 337}, {"x": 1211, "y": 337}, {"x": 1211, "y": 349}, {"x": 1184, "y": 349}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1211, "y": 337}, {"x": 1214, "y": 337}, {"x": 1214, "y": 349}, {"x": 1211, "y": 349}]}}, {"text": "www.quality.marcegaglia.com/marcegaglia-plates-dop", "bounding_poly": {"vertices": [{"x": 743, "y": 355}, {"x": 1031, "y": 355}, {"x": 1031, "y": 367}, {"x": 743, "y": 367}]}}]}, {"text": "с", "bounding_poly": {"vertices": [{"x": 172, "y": 507}, {"x": 181, "y": 507}, {"x": 181, "y": 515}, {"x": 172, "y": 515}]}, "elements": [{"text": "с", "bounding_poly": {"vertices": [{"x": 172, "y": 507}, {"x": 181, "y": 507}, {"x": 181, "y": 515}, {"x": 172, "y": 515}]}}]}, {"text": "Mn", "bounding_poly": {"vertices": [{"x": 240, "y": 507}, {"x": 257, "y": 507}, {"x": 257, "y": 515}, {"x": 240, "y": 515}]}, "elements": [{"text": "Mn", "bounding_poly": {"vertices": [{"x": 240, "y": 507}, {"x": 257, "y": 507}, {"x": 257, "y": 515}, {"x": 240, "y": 515}]}}]}, {"text": "Si", "bounding_poly": {"vertices": [{"x": 307, "y": 506}, {"x": 317, "y": 506}, {"x": 317, "y": 515}, {"x": 307, "y": 515}]}, "elements": [{"text": "Si", "bounding_poly": {"vertices": [{"x": 307, "y": 506}, {"x": 317, "y": 506}, {"x": 317, "y": 515}, {"x": 307, "y": 515}]}}]}, {"text": "Batch / Bund Nr", "bounding_poly": {"vertices": [{"x": 42, "y": 523}, {"x": 121, "y": 523}, {"x": 121, "y": 532}, {"x": 42, "y": 532}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 42, "y": 523}, {"x": 72, "y": 523}, {"x": 72, "y": 532}, {"x": 42, "y": 532}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 72, "y": 523}, {"x": 76, "y": 523}, {"x": 76, "y": 532}, {"x": 72, "y": 532}]}}, {"text": "Bund", "bounding_poly": {"vertices": [{"x": 76, "y": 523}, {"x": 103, "y": 523}, {"x": 103, "y": 532}, {"x": 76, "y": 532}]}}, {"text": "Nr", "bounding_poly": {"vertices": [{"x": 107, "y": 523}, {"x": 121, "y": 523}, {"x": 121, "y": 532}, {"x": 107, "y": 532}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 171, "y": 523}, {"x": 191, "y": 523}, {"x": 191, "y": 534}, {"x": 171, "y": 534}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 171, "y": 523}, {"x": 178, "y": 523}, {"x": 178, "y": 534}, {"x": 171, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 175, "y": 523}, {"x": 188, "y": 523}, {"x": 188, "y": 534}, {"x": 175, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 185, "y": 523}, {"x": 191, "y": 523}, {"x": 191, "y": 534}, {"x": 185, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 240, "y": 523}, {"x": 258, "y": 523}, {"x": 258, "y": 534}, {"x": 240, "y": 534}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 240, "y": 523}, {"x": 245, "y": 523}, {"x": 245, "y": 534}, {"x": 240, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 243, "y": 523}, {"x": 256, "y": 523}, {"x": 256, "y": 534}, {"x": 243, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 252, "y": 523}, {"x": 258, "y": 523}, {"x": 258, "y": 534}, {"x": 252, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 308, "y": 523}, {"x": 327, "y": 523}, {"x": 327, "y": 533}, {"x": 308, "y": 533}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 308, "y": 523}, {"x": 313, "y": 523}, {"x": 313, "y": 533}, {"x": 308, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 311, "y": 523}, {"x": 323, "y": 523}, {"x": 323, "y": 533}, {"x": 311, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 321, "y": 523}, {"x": 327, "y": 523}, {"x": 327, "y": 533}, {"x": 321, "y": 533}]}}]}, {"text": "ཨཙྩེ", "bounding_poly": {"vertices": [{"x": 336, "y": 507}, {"x": 337, "y": 533}, {"x": 306, "y": 534}, {"x": 305, "y": 508}]}, "elements": [{"text": "ཨཙྩེ", "bounding_poly": {"vertices": [{"x": 336, "y": 507}, {"x": 337, "y": 533}, {"x": 306, "y": 534}, {"x": 305, "y": 508}]}}]}, {"text": "S ( % )", "bounding_poly": {"vertices": [{"x": 375, "y": 507}, {"x": 395, "y": 507}, {"x": 395, "y": 534}, {"x": 375, "y": 534}]}, "elements": [{"text": "S", "bounding_poly": {"vertices": [{"x": 375, "y": 507}, {"x": 386, "y": 507}, {"x": 386, "y": 517}, {"x": 375, "y": 517}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 375, "y": 523}, {"x": 381, "y": 523}, {"x": 381, "y": 534}, {"x": 375, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 379, "y": 523}, {"x": 392, "y": 523}, {"x": 392, "y": 534}, {"x": 379, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 389, "y": 523}, {"x": 395, "y": 523}, {"x": 395, "y": 534}, {"x": 389, "y": 534}]}}]}, {"text": "P ( % )", "bounding_poly": {"vertices": [{"x": 443, "y": 507}, {"x": 463, "y": 507}, {"x": 463, "y": 533}, {"x": 443, "y": 533}]}, "elements": [{"text": "P", "bounding_poly": {"vertices": [{"x": 443, "y": 507}, {"x": 452, "y": 507}, {"x": 452, "y": 515}, {"x": 443, "y": 515}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 443, "y": 523}, {"x": 450, "y": 523}, {"x": 450, "y": 533}, {"x": 443, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 447, "y": 523}, {"x": 459, "y": 523}, {"x": 459, "y": 533}, {"x": 447, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 457, "y": 523}, {"x": 463, "y": 523}, {"x": 463, "y": 533}, {"x": 457, "y": 533}]}}]}, {"text": "0.2", "bounding_poly": {"vertices": [{"x": 172, "y": 557}, {"x": 188, "y": 557}, {"x": 188, "y": 565}, {"x": 172, "y": 565}]}, "elements": [{"text": "0.2", "bounding_poly": {"vertices": [{"x": 172, "y": 557}, {"x": 188, "y": 557}, {"x": 188, "y": 565}, {"x": 172, "y": 565}]}}]}, {"text": "1.6", "bounding_poly": {"vertices": [{"x": 240, "y": 557}, {"x": 257, "y": 557}, {"x": 257, "y": 565}, {"x": 240, "y": 565}]}, "elements": [{"text": "1.6", "bounding_poly": {"vertices": [{"x": 240, "y": 557}, {"x": 257, "y": 557}, {"x": 257, "y": 565}, {"x": 240, "y": 565}]}}]}, {"text": "વ", "bounding_poly": {"vertices": [{"x": 261, "y": 559}, {"x": 261, "y": 565}, {"x": 240, "y": 565}, {"x": 240, "y": 559}]}, "elements": [{"text": "વ", "bounding_poly": {"vertices": [{"x": 261, "y": 559}, {"x": 261, "y": 565}, {"x": 240, "y": 565}, {"x": 240, "y": 559}]}}]}, {"text": "0.55", "bounding_poly": {"vertices": [{"x": 308, "y": 557}, {"x": 331, "y": 557}, {"x": 331, "y": 566}, {"x": 308, "y": 566}]}, "elements": [{"text": "0.55", "bounding_poly": {"vertices": [{"x": 308, "y": 557}, {"x": 331, "y": 557}, {"x": 331, "y": 566}, {"x": 308, "y": 566}]}}]}, {"text": "0.025", "bounding_poly": {"vertices": [{"x": 375, "y": 556}, {"x": 404, "y": 556}, {"x": 404, "y": 566}, {"x": 375, "y": 566}]}, "elements": [{"text": "0.025", "bounding_poly": {"vertices": [{"x": 375, "y": 556}, {"x": 404, "y": 556}, {"x": 404, "y": 566}, {"x": 375, "y": 566}]}}]}, {"text": "n  ིི །", "bounding_poly": {"vertices": [{"x": 439, "y": 507}, {"x": 468, "y": 507}, {"x": 469, "y": 576}, {"x": 440, "y": 576}]}, "elements": [{"text": "n", "bounding_poly": {"vertices": [{"x": 440, "y": 507}, {"x": 467, "y": 507}, {"x": 467, "y": 510}, {"x": 440, "y": 510}]}}, {"text": "ིི", "bounding_poly": {"vertices": [{"x": 440, "y": 557}, {"x": 467, "y": 557}, {"x": 467, "y": 568}, {"x": 440, "y": 568}]}}, {"text": "།", "bounding_poly": {"vertices": [{"x": 440, "y": 573}, {"x": 467, "y": 573}, {"x": 467, "y": 576}, {"x": 440, "y": 576}]}}]}, {"text": "Cr", "bounding_poly": {"vertices": [{"x": 512, "y": 507}, {"x": 526, "y": 507}, {"x": 526, "y": 515}, {"x": 512, "y": 515}]}, "elements": [{"text": "Cr", "bounding_poly": {"vertices": [{"x": 512, "y": 507}, {"x": 526, "y": 507}, {"x": 526, "y": 515}, {"x": 512, "y": 515}]}}]}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 580, "y": 507}, {"x": 592, "y": 507}, {"x": 592, "y": 516}, {"x": 580, "y": 516}]}, "elements": [{"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 580, "y": 507}, {"x": 592, "y": 507}, {"x": 592, "y": 516}, {"x": 580, "y": 516}]}}]}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 648, "y": 507}, {"x": 664, "y": 507}, {"x": 664, "y": 515}, {"x": 648, "y": 515}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 648, "y": 507}, {"x": 664, "y": 507}, {"x": 664, "y": 515}, {"x": 648, "y": 515}]}}]}, {"text": "Al", "bounding_poly": {"vertices": [{"x": 716, "y": 507}, {"x": 727, "y": 507}, {"x": 727, "y": 516}, {"x": 716, "y": 516}]}, "elements": [{"text": "Al", "bounding_poly": {"vertices": [{"x": 716, "y": 507}, {"x": 727, "y": 507}, {"x": 727, "y": 516}, {"x": 716, "y": 516}]}}]}, {"text": "Mo", "bounding_poly": {"vertices": [{"x": 784, "y": 507}, {"x": 800, "y": 507}, {"x": 800, "y": 515}, {"x": 784, "y": 515}]}, "elements": [{"text": "Mo", "bounding_poly": {"vertices": [{"x": 784, "y": 507}, {"x": 800, "y": 507}, {"x": 800, "y": 515}, {"x": 784, "y": 515}]}}]}, {"text": "Nb", "bounding_poly": {"vertices": [{"x": 852, "y": 508}, {"x": 867, "y": 508}, {"x": 867, "y": 516}, {"x": 852, "y": 516}]}, "elements": [{"text": "Nb", "bounding_poly": {"vertices": [{"x": 852, "y": 508}, {"x": 867, "y": 508}, {"x": 867, "y": 516}, {"x": 852, "y": 516}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 511, "y": 523}, {"x": 532, "y": 523}, {"x": 532, "y": 535}, {"x": 511, "y": 535}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 511, "y": 523}, {"x": 517, "y": 523}, {"x": 517, "y": 535}, {"x": 511, "y": 535}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 515, "y": 523}, {"x": 528, "y": 523}, {"x": 528, "y": 535}, {"x": 515, "y": 535}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 526, "y": 523}, {"x": 532, "y": 523}, {"x": 532, "y": 535}, {"x": 526, "y": 535}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 579, "y": 523}, {"x": 598, "y": 523}, {"x": 598, "y": 534}, {"x": 579, "y": 534}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 579, "y": 523}, {"x": 585, "y": 523}, {"x": 585, "y": 534}, {"x": 579, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 582, "y": 523}, {"x": 595, "y": 523}, {"x": 595, "y": 534}, {"x": 582, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 592, "y": 523}, {"x": 598, "y": 523}, {"x": 598, "y": 534}, {"x": 592, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 647, "y": 523}, {"x": 667, "y": 523}, {"x": 667, "y": 534}, {"x": 647, "y": 534}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 647, "y": 523}, {"x": 654, "y": 523}, {"x": 654, "y": 534}, {"x": 647, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 651, "y": 523}, {"x": 664, "y": 523}, {"x": 664, "y": 534}, {"x": 651, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 661, "y": 523}, {"x": 667, "y": 523}, {"x": 667, "y": 534}, {"x": 661, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 716, "y": 523}, {"x": 734, "y": 523}, {"x": 734, "y": 533}, {"x": 716, "y": 533}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 716, "y": 523}, {"x": 722, "y": 523}, {"x": 722, "y": 533}, {"x": 716, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 718, "y": 523}, {"x": 731, "y": 523}, {"x": 731, "y": 533}, {"x": 718, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 728, "y": 523}, {"x": 734, "y": 523}, {"x": 734, "y": 533}, {"x": 728, "y": 533}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 784, "y": 523}, {"x": 802, "y": 523}, {"x": 802, "y": 533}, {"x": 784, "y": 533}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 784, "y": 523}, {"x": 789, "y": 523}, {"x": 789, "y": 533}, {"x": 784, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 786, "y": 523}, {"x": 798, "y": 523}, {"x": 798, "y": 533}, {"x": 786, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 797, "y": 523}, {"x": 802, "y": 523}, {"x": 802, "y": 533}, {"x": 797, "y": 533}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 852, "y": 523}, {"x": 870, "y": 523}, {"x": 870, "y": 533}, {"x": 852, "y": 533}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 852, "y": 523}, {"x": 857, "y": 523}, {"x": 857, "y": 533}, {"x": 852, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 854, "y": 523}, {"x": 866, "y": 523}, {"x": 866, "y": 533}, {"x": 854, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 865, "y": 523}, {"x": 870, "y": 523}, {"x": 870, "y": 533}, {"x": 865, "y": 533}]}}]}, {"text": "རྩེད", "bounding_poly": {"vertices": [{"x": 920, "y": 535}, {"x": 919, "y": 509}, {"x": 936, "y": 508}, {"x": 937, "y": 534}]}, "elements": [{"text": "རྩེད", "bounding_poly": {"vertices": [{"x": 920, "y": 535}, {"x": 919, "y": 509}, {"x": 936, "y": 508}, {"x": 937, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 919, "y": 523}, {"x": 939, "y": 523}, {"x": 939, "y": 535}, {"x": 919, "y": 535}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 919, "y": 523}, {"x": 925, "y": 523}, {"x": 925, "y": 535}, {"x": 919, "y": 535}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 923, "y": 523}, {"x": 936, "y": 523}, {"x": 936, "y": 535}, {"x": 923, "y": 535}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 933, "y": 523}, {"x": 939, "y": 523}, {"x": 939, "y": 535}, {"x": 933, "y": 535}]}}]}, {"text": "Ti ( % )", "bounding_poly": {"vertices": [{"x": 987, "y": 506}, {"x": 1008, "y": 506}, {"x": 1008, "y": 534}, {"x": 987, "y": 534}]}, "elements": [{"text": "Ti", "bounding_poly": {"vertices": [{"x": 988, "y": 506}, {"x": 996, "y": 506}, {"x": 996, "y": 516}, {"x": 988, "y": 516}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 987, "y": 523}, {"x": 993, "y": 523}, {"x": 993, "y": 534}, {"x": 987, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 991, "y": 523}, {"x": 1004, "y": 523}, {"x": 1004, "y": 534}, {"x": 991, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1002, "y": 523}, {"x": 1008, "y": 523}, {"x": 1008, "y": 534}, {"x": 1002, "y": 534}]}}]}, {"text": "N", "bounding_poly": {"vertices": [{"x": 1055, "y": 507}, {"x": 1064, "y": 507}, {"x": 1064, "y": 515}, {"x": 1055, "y": 515}]}, "elements": [{"text": "N", "bounding_poly": {"vertices": [{"x": 1055, "y": 507}, {"x": 1064, "y": 507}, {"x": 1064, "y": 515}, {"x": 1055, "y": 515}]}}]}, {"text": "Z", "bounding_poly": {"vertices": [{"x": 1056, "y": 520}, {"x": 1056, "y": 505}, {"x": 1066, "y": 505}, {"x": 1066, "y": 520}]}, "elements": [{"text": "Z", "bounding_poly": {"vertices": [{"x": 1056, "y": 520}, {"x": 1056, "y": 505}, {"x": 1066, "y": 505}, {"x": 1066, "y": 520}]}}]}, {"text": "Ceq", "bounding_poly": {"vertices": [{"x": 1124, "y": 507}, {"x": 1146, "y": 508}, {"x": 1146, "y": 518}, {"x": 1124, "y": 517}]}, "elements": [{"text": "Ceq", "bounding_poly": {"vertices": [{"x": 1124, "y": 507}, {"x": 1146, "y": 508}, {"x": 1146, "y": 518}, {"x": 1124, "y": 517}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 1056, "y": 523}, {"x": 1074, "y": 523}, {"x": 1074, "y": 534}, {"x": 1056, "y": 534}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 1056, "y": 523}, {"x": 1061, "y": 523}, {"x": 1061, "y": 534}, {"x": 1056, "y": 534}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 1058, "y": 523}, {"x": 1070, "y": 523}, {"x": 1070, "y": 534}, {"x": 1058, "y": 534}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1069, "y": 523}, {"x": 1074, "y": 523}, {"x": 1074, "y": 534}, {"x": 1069, "y": 534}]}}]}, {"text": "( % )", "bounding_poly": {"vertices": [{"x": 1124, "y": 523}, {"x": 1142, "y": 523}, {"x": 1142, "y": 533}, {"x": 1124, "y": 533}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 1124, "y": 523}, {"x": 1129, "y": 523}, {"x": 1129, "y": 533}, {"x": 1124, "y": 533}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 1126, "y": 523}, {"x": 1139, "y": 523}, {"x": 1139, "y": 533}, {"x": 1126, "y": 533}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1137, "y": 523}, {"x": 1142, "y": 523}, {"x": 1142, "y": 533}, {"x": 1137, "y": 533}]}}]}, {"text": "0.02", "bounding_poly": {"vertices": [{"x": 715, "y": 541}, {"x": 740, "y": 541}, {"x": 740, "y": 550}, {"x": 715, "y": 550}]}, "elements": [{"text": "0.02", "bounding_poly": {"vertices": [{"x": 715, "y": 541}, {"x": 740, "y": 541}, {"x": 740, "y": 550}, {"x": 715, "y": 550}]}}]}, {"text": "0.025", "bounding_poly": {"vertices": [{"x": 444, "y": 557}, {"x": 472, "y": 557}, {"x": 472, "y": 566}, {"x": 444, "y": 566}]}, "elements": [{"text": "0.025", "bounding_poly": {"vertices": [{"x": 444, "y": 557}, {"x": 472, "y": 557}, {"x": 472, "y": 566}, {"x": 444, "y": 566}]}}]}, {"text": "0.3", "bounding_poly": {"vertices": [{"x": 512, "y": 557}, {"x": 529, "y": 557}, {"x": 529, "y": 566}, {"x": 512, "y": 566}]}, "elements": [{"text": "0.3", "bounding_poly": {"vertices": [{"x": 512, "y": 557}, {"x": 529, "y": 557}, {"x": 529, "y": 566}, {"x": 512, "y": 566}]}}]}, {"text": "0.3", "bounding_poly": {"vertices": [{"x": 579, "y": 557}, {"x": 596, "y": 557}, {"x": 596, "y": 565}, {"x": 579, "y": 565}]}, "elements": [{"text": "0.3", "bounding_poly": {"vertices": [{"x": 579, "y": 557}, {"x": 596, "y": 557}, {"x": 596, "y": 565}, {"x": 579, "y": 565}]}}]}, {"text": "0.55", "bounding_poly": {"vertices": [{"x": 648, "y": 556}, {"x": 671, "y": 556}, {"x": 671, "y": 566}, {"x": 648, "y": 566}]}, "elements": [{"text": "0.55", "bounding_poly": {"vertices": [{"x": 648, "y": 556}, {"x": 671, "y": 556}, {"x": 671, "y": 566}, {"x": 648, "y": 566}]}}]}, {"text": "0.3", "bounding_poly": {"vertices": [{"x": 715, "y": 558}, {"x": 733, "y": 558}, {"x": 733, "y": 566}, {"x": 715, "y": 566}]}, "elements": [{"text": "0.3", "bounding_poly": {"vertices": [{"x": 715, "y": 558}, {"x": 733, "y": 558}, {"x": 733, "y": 566}, {"x": 715, "y": 566}]}}]}, {"text": "0.08", "bounding_poly": {"vertices": [{"x": 783, "y": 556}, {"x": 805, "y": 556}, {"x": 805, "y": 565}, {"x": 783, "y": 565}]}, "elements": [{"text": "0.08", "bounding_poly": {"vertices": [{"x": 783, "y": 556}, {"x": 805, "y": 556}, {"x": 805, "y": 565}, {"x": 783, "y": 565}]}}]}, {"text": "0.06", "bounding_poly": {"vertices": [{"x": 852, "y": 556}, {"x": 874, "y": 556}, {"x": 874, "y": 566}, {"x": 852, "y": 566}]}, "elements": [{"text": "0.06", "bounding_poly": {"vertices": [{"x": 852, "y": 556}, {"x": 874, "y": 556}, {"x": 874, "y": 566}, {"x": 852, "y": 566}]}}]}, {"text": "0.1", "bounding_poly": {"vertices": [{"x": 919, "y": 557}, {"x": 934, "y": 557}, {"x": 934, "y": 566}, {"x": 919, "y": 566}]}, "elements": [{"text": "0.1", "bounding_poly": {"vertices": [{"x": 919, "y": 557}, {"x": 934, "y": 557}, {"x": 934, "y": 566}, {"x": 919, "y": 566}]}}]}, {"text": "0.05", "bounding_poly": {"vertices": [{"x": 988, "y": 557}, {"x": 1010, "y": 557}, {"x": 1010, "y": 566}, {"x": 988, "y": 566}]}, "elements": [{"text": "0.05", "bounding_poly": {"vertices": [{"x": 988, "y": 557}, {"x": 1010, "y": 557}, {"x": 1010, "y": 566}, {"x": 988, "y": 566}]}}]}, {"text": "0.012", "bounding_poly": {"vertices": [{"x": 1055, "y": 557}, {"x": 1086, "y": 557}, {"x": 1086, "y": 566}, {"x": 1055, "y": 566}]}, "elements": [{"text": "0.012", "bounding_poly": {"vertices": [{"x": 1055, "y": 557}, {"x": 1086, "y": 557}, {"x": 1086, "y": 566}, {"x": 1055, "y": 566}]}}]}, {"text": "0.45", "bounding_poly": {"vertices": [{"x": 1124, "y": 557}, {"x": 1146, "y": 557}, {"x": 1146, "y": 566}, {"x": 1124, "y": 566}]}, "elements": [{"text": "0.45", "bounding_poly": {"vertices": [{"x": 1124, "y": 557}, {"x": 1146, "y": 557}, {"x": 1146, "y": 566}, {"x": 1124, "y": 566}]}}]}, {"text": "24EH004442 .150 1.500 .230 24EH004443 .150 1.510 .230", "bounding_poly": {"vertices": [{"x": 42, "y": 582}, {"x": 339, "y": 577}, {"x": 340, "y": 622}, {"x": 43, "y": 627}]}, "elements": [{"text": "24EH004442", "bounding_poly": {"vertices": [{"x": 42, "y": 587}, {"x": 135, "y": 587}, {"x": 135, "y": 598}, {"x": 42, "y": 598}]}}, {"text": ".150", "bounding_poly": {"vertices": [{"x": 173, "y": 585}, {"x": 202, "y": 586}, {"x": 202, "y": 600}, {"x": 173, "y": 599}]}}, {"text": "1.500", "bounding_poly": {"vertices": [{"x": 240, "y": 585}, {"x": 278, "y": 584}, {"x": 278, "y": 597}, {"x": 240, "y": 598}]}}, {"text": ".230", "bounding_poly": {"vertices": [{"x": 309, "y": 585}, {"x": 338, "y": 586}, {"x": 337, "y": 598}, {"x": 308, "y": 597}]}}, {"text": "24EH004443", "bounding_poly": {"vertices": [{"x": 42, "y": 610}, {"x": 133, "y": 610}, {"x": 133, "y": 622}, {"x": 42, "y": 622}]}}, {"text": ".150", "bounding_poly": {"vertices": [{"x": 172, "y": 610}, {"x": 202, "y": 608}, {"x": 203, "y": 621}, {"x": 173, "y": 623}]}}, {"text": "1.510", "bounding_poly": {"vertices": [{"x": 240, "y": 609}, {"x": 279, "y": 608}, {"x": 279, "y": 623}, {"x": 241, "y": 624}]}}, {"text": ".230", "bounding_poly": {"vertices": [{"x": 309, "y": 609}, {"x": 339, "y": 610}, {"x": 339, "y": 622}, {"x": 309, "y": 621}]}}]}, {"text": ".0060", "bounding_poly": {"vertices": [{"x": 376, "y": 586}, {"x": 414, "y": 585}, {"x": 414, "y": 597}, {"x": 376, "y": 598}]}, "elements": [{"text": ".0060", "bounding_poly": {"vertices": [{"x": 376, "y": 586}, {"x": 414, "y": 585}, {"x": 414, "y": 597}, {"x": 376, "y": 598}]}}]}, {"text": ".0160", "bounding_poly": {"vertices": [{"x": 444, "y": 586}, {"x": 483, "y": 584}, {"x": 484, "y": 597}, {"x": 445, "y": 599}]}, "elements": [{"text": ".0160", "bounding_poly": {"vertices": [{"x": 444, "y": 586}, {"x": 483, "y": 584}, {"x": 484, "y": 597}, {"x": 445, "y": 599}]}}]}, {"text": ".020", "bounding_poly": {"vertices": [{"x": 512, "y": 587}, {"x": 542, "y": 587}, {"x": 542, "y": 597}, {"x": 512, "y": 597}]}, "elements": [{"text": ".020", "bounding_poly": {"vertices": [{"x": 512, "y": 587}, {"x": 542, "y": 587}, {"x": 542, "y": 597}, {"x": 512, "y": 597}]}}]}, {"text": ".010", "bounding_poly": {"vertices": [{"x": 580, "y": 587}, {"x": 608, "y": 586}, {"x": 609, "y": 597}, {"x": 581, "y": 598}]}, "elements": [{"text": ".010", "bounding_poly": {"vertices": [{"x": 580, "y": 587}, {"x": 608, "y": 586}, {"x": 609, "y": 597}, {"x": 581, "y": 598}]}}]}, {"text": ".020", "bounding_poly": {"vertices": [{"x": 649, "y": 587}, {"x": 679, "y": 587}, {"x": 679, "y": 597}, {"x": 649, "y": 597}]}, "elements": [{"text": ".020", "bounding_poly": {"vertices": [{"x": 649, "y": 587}, {"x": 679, "y": 587}, {"x": 679, "y": 597}, {"x": 649, "y": 597}]}}]}, {"text": ".034", "bounding_poly": {"vertices": [{"x": 716, "y": 585}, {"x": 747, "y": 586}, {"x": 747, "y": 599}, {"x": 716, "y": 598}]}, "elements": [{"text": ".034", "bounding_poly": {"vertices": [{"x": 716, "y": 585}, {"x": 747, "y": 586}, {"x": 747, "y": 599}, {"x": 716, "y": 598}]}}]}, {"text": ".0000", "bounding_poly": {"vertices": [{"x": 784, "y": 586}, {"x": 823, "y": 587}, {"x": 823, "y": 599}, {"x": 784, "y": 598}]}, "elements": [{"text": ".0000", "bounding_poly": {"vertices": [{"x": 784, "y": 586}, {"x": 823, "y": 587}, {"x": 823, "y": 599}, {"x": 784, "y": 598}]}}]}, {"text": ".032", "bounding_poly": {"vertices": [{"x": 852, "y": 587}, {"x": 881, "y": 586}, {"x": 882, "y": 596}, {"x": 853, "y": 598}]}, "elements": [{"text": ".032", "bounding_poly": {"vertices": [{"x": 852, "y": 587}, {"x": 881, "y": 586}, {"x": 882, "y": 596}, {"x": 853, "y": 598}]}}]}, {"text": ".001", "bounding_poly": {"vertices": [{"x": 920, "y": 587}, {"x": 949, "y": 587}, {"x": 949, "y": 597}, {"x": 920, "y": 597}]}, "elements": [{"text": ".001", "bounding_poly": {"vertices": [{"x": 920, "y": 587}, {"x": 949, "y": 587}, {"x": 949, "y": 597}, {"x": 920, "y": 597}]}}]}, {"text": ".0030 .0020 .41", "bounding_poly": {"vertices": [{"x": 988, "y": 585}, {"x": 1144, "y": 587}, {"x": 1144, "y": 600}, {"x": 988, "y": 598}]}, "elements": [{"text": ".0030", "bounding_poly": {"vertices": [{"x": 988, "y": 585}, {"x": 1028, "y": 586}, {"x": 1028, "y": 599}, {"x": 988, "y": 598}]}}, {"text": ".0020", "bounding_poly": {"vertices": [{"x": 1056, "y": 586}, {"x": 1096, "y": 587}, {"x": 1096, "y": 599}, {"x": 1056, "y": 598}]}}, {"text": ".41", "bounding_poly": {"vertices": [{"x": 1124, "y": 587}, {"x": 1144, "y": 587}, {"x": 1144, "y": 597}, {"x": 1124, "y": 597}]}}]}, {"text": ".0050 .0160", "bounding_poly": {"vertices": [{"x": 376, "y": 611}, {"x": 482, "y": 611}, {"x": 482, "y": 622}, {"x": 376, "y": 622}]}, "elements": [{"text": ".0050", "bounding_poly": {"vertices": [{"x": 376, "y": 611}, {"x": 416, "y": 611}, {"x": 416, "y": 621}, {"x": 376, "y": 621}]}}, {"text": ".0160", "bounding_poly": {"vertices": [{"x": 445, "y": 611}, {"x": 482, "y": 611}, {"x": 482, "y": 622}, {"x": 445, "y": 622}]}}]}, {"text": ".010", "bounding_poly": {"vertices": [{"x": 512, "y": 611}, {"x": 543, "y": 611}, {"x": 543, "y": 622}, {"x": 512, "y": 622}]}, "elements": [{"text": ".010", "bounding_poly": {"vertices": [{"x": 512, "y": 611}, {"x": 543, "y": 611}, {"x": 543, "y": 622}, {"x": 512, "y": 622}]}}]}, {"text": ".010", "bounding_poly": {"vertices": [{"x": 580, "y": 611}, {"x": 611, "y": 611}, {"x": 611, "y": 622}, {"x": 580, "y": 622}]}, "elements": [{"text": ".010", "bounding_poly": {"vertices": [{"x": 580, "y": 611}, {"x": 611, "y": 611}, {"x": 611, "y": 622}, {"x": 580, "y": 622}]}}]}, {"text": ".010", "bounding_poly": {"vertices": [{"x": 648, "y": 611}, {"x": 679, "y": 611}, {"x": 679, "y": 622}, {"x": 648, "y": 622}]}, "elements": [{"text": ".010", "bounding_poly": {"vertices": [{"x": 648, "y": 611}, {"x": 679, "y": 611}, {"x": 679, "y": 622}, {"x": 648, "y": 622}]}}]}, {"text": ".038", "bounding_poly": {"vertices": [{"x": 716, "y": 610}, {"x": 745, "y": 609}, {"x": 745, "y": 621}, {"x": 716, "y": 622}]}, "elements": [{"text": ".038", "bounding_poly": {"vertices": [{"x": 716, "y": 610}, {"x": 745, "y": 609}, {"x": 745, "y": 621}, {"x": 716, "y": 622}]}}]}, {"text": ".0000", "bounding_poly": {"vertices": [{"x": 784, "y": 610}, {"x": 824, "y": 609}, {"x": 824, "y": 621}, {"x": 784, "y": 622}]}, "elements": [{"text": ".0000", "bounding_poly": {"vertices": [{"x": 784, "y": 610}, {"x": 824, "y": 609}, {"x": 824, "y": 621}, {"x": 784, "y": 622}]}}]}, {"text": ".030", "bounding_poly": {"vertices": [{"x": 852, "y": 611}, {"x": 881, "y": 611}, {"x": 881, "y": 621}, {"x": 852, "y": 621}]}, "elements": [{"text": ".030", "bounding_poly": {"vertices": [{"x": 852, "y": 611}, {"x": 881, "y": 611}, {"x": 881, "y": 621}, {"x": 852, "y": 621}]}}]}, {"text": ".001", "bounding_poly": {"vertices": [{"x": 920, "y": 611}, {"x": 949, "y": 611}, {"x": 949, "y": 621}, {"x": 920, "y": 621}]}, "elements": [{"text": ".001", "bounding_poly": {"vertices": [{"x": 920, "y": 611}, {"x": 949, "y": 611}, {"x": 949, "y": 621}, {"x": 920, "y": 621}]}}]}, {"text": ".0040 .0020", "bounding_poly": {"vertices": [{"x": 987, "y": 610}, {"x": 1096, "y": 609}, {"x": 1096, "y": 621}, {"x": 987, "y": 622}]}, "elements": [{"text": ".0040", "bounding_poly": {"vertices": [{"x": 987, "y": 611}, {"x": 1028, "y": 611}, {"x": 1028, "y": 622}, {"x": 987, "y": 622}]}}, {"text": ".0020", "bounding_poly": {"vertices": [{"x": 1056, "y": 610}, {"x": 1096, "y": 609}, {"x": 1096, "y": 621}, {"x": 1056, "y": 622}]}}]}, {"text": ".41", "bounding_poly": {"vertices": [{"x": 1124, "y": 611}, {"x": 1144, "y": 611}, {"x": 1144, "y": 621}, {"x": 1124, "y": 621}]}, "elements": [{"text": ".41", "bounding_poly": {"vertices": [{"x": 1124, "y": 611}, {"x": 1144, "y": 611}, {"x": 1144, "y": 621}, {"x": 1124, "y": 621}]}}]}, {"text": "Forma", "bounding_poly": {"vertices": [{"x": 172, "y": 705}, {"x": 206, "y": 705}, {"x": 206, "y": 714}, {"x": 172, "y": 714}]}, "elements": [{"text": "Forma", "bounding_poly": {"vertices": [{"x": 172, "y": 705}, {"x": 206, "y": 705}, {"x": 206, "y": 714}, {"x": 172, "y": 714}]}}]}, {"text": "( 1 ) ( 2 ) ( 3 )", "bounding_poly": {"vertices": [{"x": 239, "y": 705}, {"x": 310, "y": 705}, {"x": 310, "y": 716}, {"x": 239, "y": 716}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 239, "y": 705}, {"x": 244, "y": 705}, {"x": 244, "y": 716}, {"x": 239, "y": 716}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 243, "y": 705}, {"x": 249, "y": 705}, {"x": 249, "y": 716}, {"x": 243, "y": 716}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 250, "y": 705}, {"x": 256, "y": 705}, {"x": 256, "y": 716}, {"x": 250, "y": 716}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 268, "y": 705}, {"x": 273, "y": 705}, {"x": 273, "y": 716}, {"x": 268, "y": 716}]}}, {"text": "2", "bounding_poly": {"vertices": [{"x": 271, "y": 705}, {"x": 279, "y": 705}, {"x": 279, "y": 716}, {"x": 271, "y": 716}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 278, "y": 705}, {"x": 283, "y": 705}, {"x": 283, "y": 716}, {"x": 278, "y": 716}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 296, "y": 705}, {"x": 301, "y": 705}, {"x": 301, "y": 716}, {"x": 296, "y": 716}]}}, {"text": "3", "bounding_poly": {"vertices": [{"x": 299, "y": 705}, {"x": 306, "y": 705}, {"x": 306, "y": 716}, {"x": 299, "y": 716}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 305, "y": 705}, {"x": 310, "y": 705}, {"x": 310, "y": 716}, {"x": 305, "y": 716}]}}]}, {"text": "T", "bounding_poly": {"vertices": [{"x": 323, "y": 705}, {"x": 332, "y": 705}, {"x": 332, "y": 713}, {"x": 323, "y": 713}]}, "elements": [{"text": "T", "bounding_poly": {"vertices": [{"x": 323, "y": 705}, {"x": 332, "y": 705}, {"x": 332, "y": 713}, {"x": 323, "y": 713}]}}]}, {"text": "Batch / Bund Nr", "bounding_poly": {"vertices": [{"x": 42, "y": 721}, {"x": 120, "y": 720}, {"x": 120, "y": 731}, {"x": 42, "y": 732}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 42, "y": 721}, {"x": 71, "y": 721}, {"x": 71, "y": 732}, {"x": 42, "y": 732}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 71, "y": 721}, {"x": 75, "y": 721}, {"x": 75, "y": 731}, {"x": 71, "y": 731}]}}, {"text": "Bund", "bounding_poly": {"vertices": [{"x": 75, "y": 721}, {"x": 102, "y": 721}, {"x": 102, "y": 731}, {"x": 75, "y": 731}]}}, {"text": "Nr", "bounding_poly": {"vertices": [{"x": 106, "y": 721}, {"x": 120, "y": 721}, {"x": 120, "y": 731}, {"x": 106, "y": 731}]}}]}, {"text": "20", "bounding_poly": {"vertices": [{"x": 324, "y": 740}, {"x": 335, "y": 740}, {"x": 335, "y": 747}, {"x": 324, "y": 747}]}, "elements": [{"text": "20", "bounding_poly": {"vertices": [{"x": 324, "y": 740}, {"x": 335, "y": 740}, {"x": 335, "y": 747}, {"x": 324, "y": 747}]}}]}, {"text": "- ຂົ ສ ສ ສ ສ", "bounding_poly": {"vertices": [{"x": 343, "y": 705}, {"x": 342, "y": 820}, {"x": 318, "y": 820}, {"x": 319, "y": 705}]}, "elements": [{"text": "-", "bounding_poly": {"vertices": [{"x": 342, "y": 705}, {"x": 342, "y": 714}, {"x": 319, "y": 714}, {"x": 319, "y": 705}]}}, {"text": "ຂົ", "bounding_poly": {"vertices": [{"x": 342, "y": 722}, {"x": 342, "y": 734}, {"x": 319, "y": 734}, {"x": 319, "y": 722}]}}, {"text": "ສ", "bounding_poly": {"vertices": [{"x": 342, "y": 739}, {"x": 342, "y": 748}, {"x": 319, "y": 748}, {"x": 319, "y": 739}]}}, {"text": "ສ", "bounding_poly": {"vertices": [{"x": 342, "y": 757}, {"x": 342, "y": 766}, {"x": 319, "y": 766}, {"x": 319, "y": 757}]}}, {"text": "ສ", "bounding_poly": {"vertices": [{"x": 342, "y": 786}, {"x": 342, "y": 799}, {"x": 319, "y": 799}, {"x": 319, "y": 786}]}}, {"text": "ສ", "bounding_poly": {"vertices": [{"x": 341, "y": 810}, {"x": 341, "y": 820}, {"x": 318, "y": 820}, {"x": 318, "y": 810}]}}]}, {"text": "Rm", "bounding_poly": {"vertices": [{"x": 358, "y": 706}, {"x": 375, "y": 706}, {"x": 375, "y": 714}, {"x": 358, "y": 714}]}, "elements": [{"text": "Rm", "bounding_poly": {"vertices": [{"x": 358, "y": 706}, {"x": 375, "y": 706}, {"x": 375, "y": 714}, {"x": 358, "y": 714}]}}]}, {"text": "ReH", "bounding_poly": {"vertices": [{"x": 426, "y": 705}, {"x": 450, "y": 705}, {"x": 450, "y": 714}, {"x": 426, "y": 714}]}, "elements": [{"text": "ReH", "bounding_poly": {"vertices": [{"x": 426, "y": 705}, {"x": 450, "y": 705}, {"x": 450, "y": 714}, {"x": 426, "y": 714}]}}]}, {"text": "[ ° C ] [ N / mmq ]", "bounding_poly": {"vertices": [{"x": 324, "y": 723}, {"x": 402, "y": 723}, {"x": 402, "y": 734}, {"x": 324, "y": 734}]}, "elements": [{"text": "[", "bounding_poly": {"vertices": [{"x": 324, "y": 723}, {"x": 327, "y": 723}, {"x": 327, "y": 734}, {"x": 324, "y": 734}]}}, {"text": "°", "bounding_poly": {"vertices": [{"x": 327, "y": 723}, {"x": 331, "y": 723}, {"x": 331, "y": 734}, {"x": 327, "y": 734}]}}, {"text": "C", "bounding_poly": {"vertices": [{"x": 331, "y": 723}, {"x": 339, "y": 723}, {"x": 339, "y": 734}, {"x": 331, "y": 734}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 339, "y": 723}, {"x": 343, "y": 723}, {"x": 343, "y": 734}, {"x": 339, "y": 734}]}}, {"text": "[", "bounding_poly": {"vertices": [{"x": 357, "y": 723}, {"x": 360, "y": 723}, {"x": 360, "y": 734}, {"x": 357, "y": 734}]}}, {"text": "N", "bounding_poly": {"vertices": [{"x": 360, "y": 723}, {"x": 368, "y": 723}, {"x": 368, "y": 734}, {"x": 360, "y": 734}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 369, "y": 723}, {"x": 373, "y": 723}, {"x": 373, "y": 734}, {"x": 369, "y": 734}]}}, {"text": "mmq", "bounding_poly": {"vertices": [{"x": 373, "y": 723}, {"x": 400, "y": 723}, {"x": 400, "y": 734}, {"x": 373, "y": 734}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 398, "y": 723}, {"x": 402, "y": 723}, {"x": 402, "y": 734}, {"x": 398, "y": 734}]}}]}, {"text": "[ N / mmq ]", "bounding_poly": {"vertices": [{"x": 426, "y": 722}, {"x": 471, "y": 721}, {"x": 471, "y": 734}, {"x": 426, "y": 735}]}, "elements": [{"text": "[", "bounding_poly": {"vertices": [{"x": 426, "y": 723}, {"x": 430, "y": 723}, {"x": 430, "y": 735}, {"x": 426, "y": 735}]}}, {"text": "N", "bounding_poly": {"vertices": [{"x": 428, "y": 722}, {"x": 437, "y": 722}, {"x": 437, "y": 734}, {"x": 428, "y": 734}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 436, "y": 722}, {"x": 440, "y": 722}, {"x": 440, "y": 734}, {"x": 436, "y": 734}]}}, {"text": "mmq", "bounding_poly": {"vertices": [{"x": 440, "y": 722}, {"x": 466, "y": 722}, {"x": 466, "y": 734}, {"x": 440, "y": 734}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 466, "y": 722}, {"x": 471, "y": 722}, {"x": 471, "y": 734}, {"x": 466, "y": 734}]}}]}, {"text": "470", "bounding_poly": {"vertices": [{"x": 357, "y": 740}, {"x": 378, "y": 740}, {"x": 378, "y": 748}, {"x": 357, "y": 748}]}, "elements": [{"text": "470", "bounding_poly": {"vertices": [{"x": 357, "y": 740}, {"x": 378, "y": 740}, {"x": 378, "y": 748}, {"x": 357, "y": 748}]}}]}, {"text": "345", "bounding_poly": {"vertices": [{"x": 425, "y": 740}, {"x": 444, "y": 740}, {"x": 444, "y": 747}, {"x": 425, "y": 747}]}, "elements": [{"text": "345", "bounding_poly": {"vertices": [{"x": 425, "y": 740}, {"x": 444, "y": 740}, {"x": 444, "y": 747}, {"x": 425, "y": 747}]}}]}, {"text": "A % ( % ) 20", "bounding_poly": {"vertices": [{"x": 493, "y": 705}, {"x": 512, "y": 705}, {"x": 512, "y": 748}, {"x": 493, "y": 748}]}, "elements": [{"text": "A", "bounding_poly": {"vertices": [{"x": 493, "y": 706}, {"x": 503, "y": 706}, {"x": 503, "y": 714}, {"x": 493, "y": 714}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 500, "y": 705}, {"x": 511, "y": 705}, {"x": 511, "y": 713}, {"x": 500, "y": 713}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 494, "y": 723}, {"x": 499, "y": 723}, {"x": 499, "y": 733}, {"x": 494, "y": 733}]}}, {"text": "%", "bounding_poly": {"vertices": [{"x": 496, "y": 723}, {"x": 508, "y": 723}, {"x": 508, "y": 733}, {"x": 496, "y": 733}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 506, "y": 723}, {"x": 512, "y": 723}, {"x": 512, "y": 733}, {"x": 506, "y": 733}]}}, {"text": "20", "bounding_poly": {"vertices": [{"x": 493, "y": 740}, {"x": 506, "y": 740}, {"x": 506, "y": 748}, {"x": 493, "y": 748}]}}]}, {"text": "( 1b ( 2b ) ( 3b ( 4b )", "bounding_poly": {"vertices": [{"x": 562, "y": 705}, {"x": 667, "y": 705}, {"x": 667, "y": 716}, {"x": 562, "y": 716}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 562, "y": 705}, {"x": 566, "y": 705}, {"x": 566, "y": 714}, {"x": 562, "y": 714}]}}, {"text": "1b", "bounding_poly": {"vertices": [{"x": 566, "y": 705}, {"x": 579, "y": 705}, {"x": 579, "y": 714}, {"x": 566, "y": 714}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 590, "y": 705}, {"x": 594, "y": 705}, {"x": 594, "y": 714}, {"x": 590, "y": 714}]}}, {"text": "2b", "bounding_poly": {"vertices": [{"x": 593, "y": 705}, {"x": 607, "y": 705}, {"x": 607, "y": 714}, {"x": 593, "y": 714}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 603, "y": 705}, {"x": 608, "y": 705}, {"x": 608, "y": 714}, {"x": 603, "y": 714}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 618, "y": 705}, {"x": 623, "y": 705}, {"x": 623, "y": 716}, {"x": 618, "y": 716}]}}, {"text": "3b", "bounding_poly": {"vertices": [{"x": 622, "y": 705}, {"x": 636, "y": 705}, {"x": 636, "y": 716}, {"x": 622, "y": 716}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 646, "y": 705}, {"x": 650, "y": 705}, {"x": 650, "y": 716}, {"x": 646, "y": 716}]}}, {"text": "4b", "bounding_poly": {"vertices": [{"x": 649, "y": 705}, {"x": 664, "y": 705}, {"x": 664, "y": 716}, {"x": 649, "y": 716}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 663, "y": 705}, {"x": 667, "y": 705}, {"x": 667, "y": 716}, {"x": 663, "y": 716}]}}]}, {"text": "630", "bounding_poly": {"vertices": [{"x": 358, "y": 757}, {"x": 378, "y": 757}, {"x": 378, "y": 765}, {"x": 358, "y": 765}]}, "elements": [{"text": "630", "bounding_poly": {"vertices": [{"x": 358, "y": 757}, {"x": 378, "y": 757}, {"x": 378, "y": 765}, {"x": 358, "y": 765}]}}]}, {"text": "547", "bounding_poly": {"vertices": [{"x": 352, "y": 786}, {"x": 379, "y": 786}, {"x": 379, "y": 797}, {"x": 352, "y": 797}]}, "elements": [{"text": "547", "bounding_poly": {"vertices": [{"x": 352, "y": 786}, {"x": 379, "y": 786}, {"x": 379, "y": 797}, {"x": 352, "y": 797}]}}]}, {"text": "434", "bounding_poly": {"vertices": [{"x": 421, "y": 786}, {"x": 447, "y": 785}, {"x": 448, "y": 797}, {"x": 422, "y": 798}]}, "elements": [{"text": "434", "bounding_poly": {"vertices": [{"x": 421, "y": 786}, {"x": 447, "y": 785}, {"x": 448, "y": 797}, {"x": 422, "y": 798}]}}]}, {"text": "28.1", "bounding_poly": {"vertices": [{"x": 489, "y": 787}, {"x": 519, "y": 787}, {"x": 519, "y": 798}, {"x": 489, "y": 798}]}, "elements": [{"text": "28.1", "bounding_poly": {"vertices": [{"x": 489, "y": 787}, {"x": 519, "y": 787}, {"x": 519, "y": 798}, {"x": 489, "y": 798}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 558, "y": 787}, {"x": 565, "y": 787}, {"x": 565, "y": 797}, {"x": 558, "y": 797}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 558, "y": 787}, {"x": 565, "y": 787}, {"x": 565, "y": 797}, {"x": 558, "y": 797}]}}]}, {"text": "P L 10.0", "bounding_poly": {"vertices": [{"x": 586, "y": 786}, {"x": 673, "y": 786}, {"x": 673, "y": 799}, {"x": 586, "y": 799}]}, "elements": [{"text": "P", "bounding_poly": {"vertices": [{"x": 586, "y": 786}, {"x": 598, "y": 786}, {"x": 598, "y": 797}, {"x": 586, "y": 797}]}}, {"text": "L", "bounding_poly": {"vertices": [{"x": 614, "y": 786}, {"x": 624, "y": 786}, {"x": 624, "y": 797}, {"x": 614, "y": 797}]}}, {"text": "10.0", "bounding_poly": {"vertices": [{"x": 643, "y": 787}, {"x": 673, "y": 788}, {"x": 673, "y": 799}, {"x": 643, "y": 798}]}}]}, {"text": "549", "bounding_poly": {"vertices": [{"x": 353, "y": 810}, {"x": 380, "y": 810}, {"x": 380, "y": 822}, {"x": 353, "y": 822}]}, "elements": [{"text": "549", "bounding_poly": {"vertices": [{"x": 353, "y": 810}, {"x": 380, "y": 810}, {"x": 380, "y": 822}, {"x": 353, "y": 822}]}}]}, {"text": "449", "bounding_poly": {"vertices": [{"x": 421, "y": 811}, {"x": 446, "y": 811}, {"x": 446, "y": 821}, {"x": 421, "y": 821}]}, "elements": [{"text": "449", "bounding_poly": {"vertices": [{"x": 421, "y": 811}, {"x": 446, "y": 811}, {"x": 446, "y": 821}, {"x": 421, "y": 821}]}}]}, {"text": "28.7", "bounding_poly": {"vertices": [{"x": 489, "y": 810}, {"x": 519, "y": 809}, {"x": 519, "y": 821}, {"x": 489, "y": 822}]}, "elements": [{"text": "28.7", "bounding_poly": {"vertices": [{"x": 489, "y": 810}, {"x": 519, "y": 809}, {"x": 519, "y": 821}, {"x": 489, "y": 822}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 559, "y": 811}, {"x": 566, "y": 811}, {"x": 566, "y": 820}, {"x": 559, "y": 820}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 559, "y": 811}, {"x": 566, "y": 811}, {"x": 566, "y": 820}, {"x": 559, "y": 820}]}}]}, {"text": "PL 10.0", "bounding_poly": {"vertices": [{"x": 586, "y": 806}, {"x": 673, "y": 810}, {"x": 672, "y": 824}, {"x": 585, "y": 820}]}, "elements": [{"text": "PL", "bounding_poly": {"vertices": [{"x": 586, "y": 808}, {"x": 624, "y": 810}, {"x": 623, "y": 822}, {"x": 585, "y": 820}]}}, {"text": "10.0", "bounding_poly": {"vertices": [{"x": 643, "y": 809}, {"x": 673, "y": 810}, {"x": 672, "y": 823}, {"x": 642, "y": 822}]}}]}, {"text": "1888", "bounding_poly": {"vertices": [{"x": 731, "y": 701}, {"x": 731, "y": 822}, {"x": 712, "y": 822}, {"x": 712, "y": 701}]}, "elements": [{"text": "1888", "bounding_poly": {"vertices": [{"x": 731, "y": 701}, {"x": 731, "y": 822}, {"x": 712, "y": 822}, {"x": 712, "y": 701}]}}]}, {"text": "T Kv1 Kv2", "bounding_poly": {"vertices": [{"x": 713, "y": 704}, {"x": 806, "y": 704}, {"x": 806, "y": 714}, {"x": 713, "y": 714}]}, "elements": [{"text": "T", "bounding_poly": {"vertices": [{"x": 713, "y": 705}, {"x": 721, "y": 705}, {"x": 721, "y": 713}, {"x": 713, "y": 713}]}}, {"text": "Kv1", "bounding_poly": {"vertices": [{"x": 748, "y": 705}, {"x": 767, "y": 705}, {"x": 767, "y": 714}, {"x": 748, "y": 714}]}}, {"text": "Kv2", "bounding_poly": {"vertices": [{"x": 786, "y": 704}, {"x": 806, "y": 704}, {"x": 806, "y": 714}, {"x": 786, "y": 714}]}}]}, {"text": "Kv3", "bounding_poly": {"vertices": [{"x": 824, "y": 705}, {"x": 843, "y": 705}, {"x": 843, "y": 714}, {"x": 824, "y": 714}]}, "elements": [{"text": "Kv3", "bounding_poly": {"vertices": [{"x": 824, "y": 705}, {"x": 843, "y": 705}, {"x": 843, "y": 714}, {"x": 824, "y": 714}]}}]}, {"text": "Kv", "bounding_poly": {"vertices": [{"x": 862, "y": 705}, {"x": 876, "y": 705}, {"x": 876, "y": 713}, {"x": 862, "y": 713}]}, "elements": [{"text": "Kv", "bounding_poly": {"vertices": [{"x": 862, "y": 705}, {"x": 876, "y": 705}, {"x": 876, "y": 713}, {"x": 862, "y": 713}]}}]}, {"text": "[ ° C ] [ J ] [ J ] [ J ] [ J ] \n-60 27 27 27", "bounding_poly": {"vertices": [{"x": 713, "y": 723}, {"x": 875, "y": 723}, {"x": 875, "y": 748}, {"x": 713, "y": 748}]}, "elements": [{"text": "[", "bounding_poly": {"vertices": [{"x": 713, "y": 723}, {"x": 717, "y": 723}, {"x": 717, "y": 734}, {"x": 713, "y": 734}]}}, {"text": "°", "bounding_poly": {"vertices": [{"x": 717, "y": 723}, {"x": 722, "y": 723}, {"x": 722, "y": 734}, {"x": 717, "y": 734}]}}, {"text": "C", "bounding_poly": {"vertices": [{"x": 722, "y": 723}, {"x": 730, "y": 723}, {"x": 730, "y": 734}, {"x": 722, "y": 734}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 730, "y": 723}, {"x": 734, "y": 723}, {"x": 734, "y": 734}, {"x": 730, "y": 734}]}}, {"text": "[", "bounding_poly": {"vertices": [{"x": 747, "y": 723}, {"x": 751, "y": 723}, {"x": 751, "y": 734}, {"x": 747, "y": 734}]}}, {"text": "J", "bounding_poly": {"vertices": [{"x": 751, "y": 723}, {"x": 757, "y": 723}, {"x": 757, "y": 734}, {"x": 751, "y": 734}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 757, "y": 723}, {"x": 761, "y": 723}, {"x": 761, "y": 734}, {"x": 757, "y": 734}]}}, {"text": "[", "bounding_poly": {"vertices": [{"x": 785, "y": 724}, {"x": 789, "y": 724}, {"x": 789, "y": 735}, {"x": 785, "y": 735}]}}, {"text": "J", "bounding_poly": {"vertices": [{"x": 788, "y": 724}, {"x": 795, "y": 724}, {"x": 795, "y": 735}, {"x": 788, "y": 735}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 794, "y": 724}, {"x": 798, "y": 724}, {"x": 798, "y": 735}, {"x": 794, "y": 735}]}}, {"text": "[", "bounding_poly": {"vertices": [{"x": 823, "y": 724}, {"x": 827, "y": 724}, {"x": 827, "y": 735}, {"x": 823, "y": 735}]}}, {"text": "J", "bounding_poly": {"vertices": [{"x": 826, "y": 724}, {"x": 832, "y": 724}, {"x": 832, "y": 735}, {"x": 826, "y": 735}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 832, "y": 724}, {"x": 836, "y": 724}, {"x": 836, "y": 735}, {"x": 832, "y": 735}]}}, {"text": "[", "bounding_poly": {"vertices": [{"x": 861, "y": 723}, {"x": 865, "y": 723}, {"x": 865, "y": 735}, {"x": 861, "y": 735}]}}, {"text": "J", "bounding_poly": {"vertices": [{"x": 865, "y": 723}, {"x": 871, "y": 723}, {"x": 871, "y": 735}, {"x": 865, "y": 735}]}}, {"text": "]", "bounding_poly": {"vertices": [{"x": 870, "y": 723}, {"x": 875, "y": 723}, {"x": 875, "y": 735}, {"x": 870, "y": 735}]}}, {"text": "-60", "bounding_poly": {"vertices": [{"x": 715, "y": 740}, {"x": 729, "y": 740}, {"x": 729, "y": 748}, {"x": 715, "y": 748}]}}, {"text": "27", "bounding_poly": {"vertices": [{"x": 747, "y": 740}, {"x": 761, "y": 740}, {"x": 761, "y": 748}, {"x": 747, "y": 748}]}}, {"text": "27", "bounding_poly": {"vertices": [{"x": 785, "y": 740}, {"x": 799, "y": 740}, {"x": 799, "y": 748}, {"x": 785, "y": 748}]}}, {"text": "27", "bounding_poly": {"vertices": [{"x": 823, "y": 740}, {"x": 836, "y": 740}, {"x": 836, "y": 747}, {"x": 823, "y": 747}]}}]}, {"text": "드줄", "bounding_poly": {"vertices": [{"x": 862, "y": 731}, {"x": 862, "y": 704}, {"x": 879, "y": 704}, {"x": 879, "y": 731}]}, "elements": [{"text": "드줄", "bounding_poly": {"vertices": [{"x": 862, "y": 731}, {"x": 862, "y": 704}, {"x": 879, "y": 704}, {"x": 879, "y": 731}]}}]}, {"text": "TESTN", "bounding_poly": {"vertices": [{"x": 896, "y": 705}, {"x": 935, "y": 705}, {"x": 935, "y": 714}, {"x": 896, "y": 714}]}, "elements": [{"text": "TESTN", "bounding_poly": {"vertices": [{"x": 896, "y": 705}, {"x": 935, "y": 705}, {"x": 935, "y": 714}, {"x": 896, "y": 714}]}}]}, {"text": "-20", "bounding_poly": {"vertices": [{"x": 714, "y": 758}, {"x": 730, "y": 758}, {"x": 730, "y": 766}, {"x": 714, "y": 766}]}, "elements": [{"text": "-20", "bounding_poly": {"vertices": [{"x": 714, "y": 758}, {"x": 730, "y": 758}, {"x": 730, "y": 766}, {"x": 714, "y": 766}]}}]}, {"text": "-20 242 220 201 221 24-63696", "bounding_poly": {"vertices": [{"x": 710, "y": 786}, {"x": 959, "y": 786}, {"x": 959, "y": 798}, {"x": 710, "y": 798}]}, "elements": [{"text": "-20", "bounding_poly": {"vertices": [{"x": 710, "y": 786}, {"x": 733, "y": 786}, {"x": 733, "y": 798}, {"x": 710, "y": 798}]}}, {"text": "242", "bounding_poly": {"vertices": [{"x": 743, "y": 786}, {"x": 770, "y": 786}, {"x": 770, "y": 798}, {"x": 743, "y": 798}]}}, {"text": "220", "bounding_poly": {"vertices": [{"x": 781, "y": 786}, {"x": 809, "y": 786}, {"x": 809, "y": 798}, {"x": 781, "y": 798}]}}, {"text": "201", "bounding_poly": {"vertices": [{"x": 819, "y": 786}, {"x": 845, "y": 786}, {"x": 845, "y": 798}, {"x": 819, "y": 798}]}}, {"text": "221", "bounding_poly": {"vertices": [{"x": 857, "y": 786}, {"x": 882, "y": 786}, {"x": 882, "y": 798}, {"x": 857, "y": 798}]}}, {"text": "24-63696", "bounding_poly": {"vertices": [{"x": 891, "y": 786}, {"x": 959, "y": 786}, {"x": 959, "y": 798}, {"x": 891, "y": 798}]}}]}, {"text": "-20 238 245 224 235 24-63693", "bounding_poly": {"vertices": [{"x": 710, "y": 810}, {"x": 959, "y": 810}, {"x": 959, "y": 822}, {"x": 710, "y": 822}]}, "elements": [{"text": "-20", "bounding_poly": {"vertices": [{"x": 710, "y": 810}, {"x": 733, "y": 810}, {"x": 733, "y": 822}, {"x": 710, "y": 822}]}}, {"text": "238", "bounding_poly": {"vertices": [{"x": 743, "y": 810}, {"x": 771, "y": 810}, {"x": 771, "y": 822}, {"x": 743, "y": 822}]}}, {"text": "245", "bounding_poly": {"vertices": [{"x": 781, "y": 810}, {"x": 809, "y": 810}, {"x": 809, "y": 822}, {"x": 781, "y": 822}]}}, {"text": "224", "bounding_poly": {"vertices": [{"x": 819, "y": 810}, {"x": 847, "y": 810}, {"x": 847, "y": 822}, {"x": 819, "y": 822}]}}, {"text": "235", "bounding_poly": {"vertices": [{"x": 857, "y": 810}, {"x": 884, "y": 810}, {"x": 884, "y": 822}, {"x": 857, "y": 822}]}}, {"text": "24-63693", "bounding_poly": {"vertices": [{"x": 892, "y": 810}, {"x": 959, "y": 810}, {"x": 959, "y": 822}, {"x": 892, "y": 822}]}}]}, {"text": "24EH004442", "bounding_poly": {"vertices": [{"x": 37, "y": 786}, {"x": 129, "y": 786}, {"x": 129, "y": 798}, {"x": 37, "y": 798}]}, "elements": [{"text": "24EH004442", "bounding_poly": {"vertices": [{"x": 37, "y": 786}, {"x": 129, "y": 786}, {"x": 129, "y": 798}, {"x": 37, "y": 798}]}}]}, {"text": "P", "bounding_poly": {"vertices": [{"x": 168, "y": 786}, {"x": 179, "y": 786}, {"x": 179, "y": 798}, {"x": 168, "y": 798}]}, "elements": [{"text": "P", "bounding_poly": {"vertices": [{"x": 168, "y": 786}, {"x": 179, "y": 786}, {"x": 179, "y": 798}, {"x": 168, "y": 798}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 236, "y": 786}, {"x": 243, "y": 786}, {"x": 243, "y": 795}, {"x": 236, "y": 795}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 236, "y": 786}, {"x": 243, "y": 786}, {"x": 243, "y": 795}, {"x": 236, "y": 795}]}}]}, {"text": "P T 20", "bounding_poly": {"vertices": [{"x": 264, "y": 786}, {"x": 336, "y": 786}, {"x": 336, "y": 798}, {"x": 264, "y": 798}]}, "elements": [{"text": "P", "bounding_poly": {"vertices": [{"x": 264, "y": 786}, {"x": 275, "y": 786}, {"x": 275, "y": 797}, {"x": 264, "y": 797}]}}, {"text": "T", "bounding_poly": {"vertices": [{"x": 291, "y": 786}, {"x": 302, "y": 786}, {"x": 302, "y": 797}, {"x": 291, "y": 797}]}}, {"text": "20", "bounding_poly": {"vertices": [{"x": 319, "y": 786}, {"x": 336, "y": 786}, {"x": 336, "y": 798}, {"x": 319, "y": 798}]}}]}, {"text": "24EH004443", "bounding_poly": {"vertices": [{"x": 38, "y": 811}, {"x": 130, "y": 811}, {"x": 130, "y": 822}, {"x": 38, "y": 822}]}, "elements": [{"text": "24EH004443", "bounding_poly": {"vertices": [{"x": 38, "y": 811}, {"x": 130, "y": 811}, {"x": 130, "y": 822}, {"x": 38, "y": 822}]}}]}, {"text": "P", "bounding_poly": {"vertices": [{"x": 168, "y": 810}, {"x": 179, "y": 810}, {"x": 179, "y": 820}, {"x": 168, "y": 820}]}, "elements": [{"text": "P", "bounding_poly": {"vertices": [{"x": 168, "y": 810}, {"x": 179, "y": 810}, {"x": 179, "y": 820}, {"x": 168, "y": 820}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 236, "y": 811}, {"x": 243, "y": 811}, {"x": 243, "y": 821}, {"x": 236, "y": 821}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 236, "y": 811}, {"x": 243, "y": 811}, {"x": 243, "y": 821}, {"x": 236, "y": 821}]}}]}, {"text": "PT 20", "bounding_poly": {"vertices": [{"x": 264, "y": 810}, {"x": 338, "y": 810}, {"x": 338, "y": 822}, {"x": 264, "y": 822}]}, "elements": [{"text": "PT", "bounding_poly": {"vertices": [{"x": 264, "y": 810}, {"x": 301, "y": 810}, {"x": 301, "y": 822}, {"x": 264, "y": 822}]}}, {"text": "20", "bounding_poly": {"vertices": [{"x": 319, "y": 810}, {"x": 338, "y": 810}, {"x": 338, "y": 822}, {"x": 319, "y": 822}]}}]}, {"text": "Ultrasonic control", "bounding_poly": {"vertices": [{"x": 542, "y": 893}, {"x": 635, "y": 894}, {"x": 635, "y": 903}, {"x": 542, "y": 902}]}, "elements": [{"text": "Ultrasonic", "bounding_poly": {"vertices": [{"x": 542, "y": 893}, {"x": 596, "y": 893}, {"x": 596, "y": 902}, {"x": 542, "y": 902}]}}, {"text": "control", "bounding_poly": {"vertices": [{"x": 598, "y": 893}, {"x": 635, "y": 893}, {"x": 635, "y": 902}, {"x": 598, "y": 902}]}}]}, {"text": "Firma", "bounding_poly": {"vertices": [{"x": 1053, "y": 897}, {"x": 1082, "y": 897}, {"x": 1082, "y": 905}, {"x": 1053, "y": 905}]}, "elements": [{"text": "Firma", "bounding_poly": {"vertices": [{"x": 1053, "y": 897}, {"x": 1082, "y": 897}, {"x": 1082, "y": 905}, {"x": 1053, "y": 905}]}}]}, {"text": "Equipment", "bounding_poly": {"vertices": [{"x": 32, "y": 920}, {"x": 88, "y": 919}, {"x": 88, "y": 931}, {"x": 32, "y": 932}]}, "elements": [{"text": "Equipment", "bounding_poly": {"vertices": [{"x": 32, "y": 920}, {"x": 88, "y": 919}, {"x": 88, "y": 931}, {"x": 32, "y": 932}]}}]}, {"text": "Serial number", "bounding_poly": {"vertices": [{"x": 202, "y": 920}, {"x": 275, "y": 919}, {"x": 275, "y": 929}, {"x": 202, "y": 930}]}, "elements": [{"text": "Serial", "bounding_poly": {"vertices": [{"x": 202, "y": 920}, {"x": 231, "y": 920}, {"x": 231, "y": 930}, {"x": 202, "y": 930}]}}, {"text": "number", "bounding_poly": {"vertices": [{"x": 235, "y": 920}, {"x": 275, "y": 920}, {"x": 275, "y": 929}, {"x": 235, "y": 929}]}}]}, {"text": "Surface conditions", "bounding_poly": {"vertices": [{"x": 372, "y": 921}, {"x": 471, "y": 921}, {"x": 471, "y": 930}, {"x": 372, "y": 930}]}, "elements": [{"text": "Surface", "bounding_poly": {"vertices": [{"x": 372, "y": 921}, {"x": 413, "y": 921}, {"x": 413, "y": 930}, {"x": 372, "y": 930}]}}, {"text": "conditions", "bounding_poly": {"vertices": [{"x": 417, "y": 921}, {"x": 471, "y": 921}, {"x": 471, "y": 930}, {"x": 417, "y": 930}]}}]}, {"text": "Gilardoni RDG / 700", "bounding_poly": {"vertices": [{"x": 32, "y": 942}, {"x": 131, "y": 942}, {"x": 131, "y": 953}, {"x": 32, "y": 953}]}, "elements": [{"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 32, "y": 942}, {"x": 77, "y": 942}, {"x": 77, "y": 953}, {"x": 32, "y": 953}]}}, {"text": "RDG", "bounding_poly": {"vertices": [{"x": 83, "y": 942}, {"x": 108, "y": 942}, {"x": 108, "y": 952}, {"x": 83, "y": 952}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 108, "y": 942}, {"x": 112, "y": 942}, {"x": 112, "y": 952}, {"x": 108, "y": 952}]}}, {"text": "700", "bounding_poly": {"vertices": [{"x": 112, "y": 942}, {"x": 131, "y": 942}, {"x": 131, "y": 952}, {"x": 112, "y": 952}]}}]}, {"text": "1001288", "bounding_poly": {"vertices": [{"x": 202, "y": 943}, {"x": 245, "y": 943}, {"x": 245, "y": 953}, {"x": 202, "y": 953}]}, "elements": [{"text": "1001288", "bounding_poly": {"vertices": [{"x": 202, "y": 943}, {"x": 245, "y": 943}, {"x": 245, "y": 953}, {"x": 202, "y": 953}]}}]}, {"text": "As Rolled", "bounding_poly": {"vertices": [{"x": 372, "y": 943}, {"x": 423, "y": 943}, {"x": 423, "y": 952}, {"x": 372, "y": 952}]}, "elements": [{"text": "As", "bounding_poly": {"vertices": [{"x": 372, "y": 943}, {"x": 386, "y": 943}, {"x": 386, "y": 952}, {"x": 372, "y": 952}]}}, {"text": "Rolled", "bounding_poly": {"vertices": [{"x": 389, "y": 943}, {"x": 423, "y": 943}, {"x": 423, "y": 952}, {"x": 389, "y": 952}]}}]}, {"text": "Couplant Water", "bounding_poly": {"vertices": [{"x": 541, "y": 921}, {"x": 590, "y": 921}, {"x": 590, "y": 951}, {"x": 541, "y": 951}]}, "elements": [{"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 542, "y": 921}, {"x": 590, "y": 921}, {"x": 590, "y": 932}, {"x": 542, "y": 932}]}}, {"text": "Water", "bounding_poly": {"vertices": [{"x": 541, "y": 942}, {"x": 573, "y": 942}, {"x": 573, "y": 951}, {"x": 541, "y": 951}]}}]}, {"text": "Procedure", "bounding_poly": {"vertices": [{"x": 712, "y": 921}, {"x": 767, "y": 921}, {"x": 767, "y": 931}, {"x": 712, "y": 931}]}, "elements": [{"text": "Procedure", "bounding_poly": {"vertices": [{"x": 712, "y": 921}, {"x": 767, "y": 921}, {"x": 767, "y": 931}, {"x": 712, "y": 931}]}}]}, {"text": "According To Specification", "bounding_poly": {"vertices": [{"x": 711, "y": 942}, {"x": 854, "y": 943}, {"x": 854, "y": 955}, {"x": 711, "y": 954}]}, "elements": [{"text": "According", "bounding_poly": {"vertices": [{"x": 711, "y": 942}, {"x": 765, "y": 942}, {"x": 765, "y": 954}, {"x": 711, "y": 954}]}}, {"text": "To", "bounding_poly": {"vertices": [{"x": 768, "y": 942}, {"x": 782, "y": 942}, {"x": 782, "y": 954}, {"x": 768, "y": 954}]}}, {"text": "Specification", "bounding_poly": {"vertices": [{"x": 785, "y": 942}, {"x": 854, "y": 942}, {"x": 854, "y": 954}, {"x": 785, "y": 954}]}}]}, {"text": "Calibration According To Specification", "bounding_poly": {"vertices": [{"x": 881, "y": 921}, {"x": 1022, "y": 920}, {"x": 1022, "y": 954}, {"x": 881, "y": 955}]}, "elements": [{"text": "Calibration", "bounding_poly": {"vertices": [{"x": 882, "y": 921}, {"x": 939, "y": 921}, {"x": 939, "y": 930}, {"x": 882, "y": 930}]}}, {"text": "According", "bounding_poly": {"vertices": [{"x": 881, "y": 943}, {"x": 934, "y": 943}, {"x": 934, "y": 955}, {"x": 881, "y": 955}]}}, {"text": "To", "bounding_poly": {"vertices": [{"x": 938, "y": 943}, {"x": 952, "y": 943}, {"x": 952, "y": 954}, {"x": 938, "y": 954}]}}, {"text": "Specification", "bounding_poly": {"vertices": [{"x": 956, "y": 942}, {"x": 1022, "y": 942}, {"x": 1022, "y": 954}, {"x": 956, "y": 954}]}}]}, {"text": "IIllivEN / ISO9712", "bounding_poly": {"vertices": [{"x": 1052, "y": 919}, {"x": 1139, "y": 920}, {"x": 1139, "y": 929}, {"x": 1052, "y": 928}]}, "elements": [{"text": "IIllivEN", "bounding_poly": {"vertices": [{"x": 1052, "y": 919}, {"x": 1087, "y": 919}, {"x": 1087, "y": 928}, {"x": 1052, "y": 928}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1090, "y": 919}, {"x": 1094, "y": 919}, {"x": 1094, "y": 928}, {"x": 1090, "y": 928}]}}, {"text": "ISO9712", "bounding_poly": {"vertices": [{"x": 1093, "y": 919}, {"x": 1139, "y": 919}, {"x": 1139, "y": 928}, {"x": 1093, "y": 928}]}}]}, {"text": "Q.M.D<PERSON>", "bounding_poly": {"vertices": [{"x": 1052, "y": 936}, {"x": 1158, "y": 936}, {"x": 1158, "y": 948}, {"x": 1052, "y": 948}]}, "elements": [{"text": "Q.M.D.", "bounding_poly": {"vertices": [{"x": 1052, "y": 936}, {"x": 1091, "y": 936}, {"x": 1091, "y": 948}, {"x": 1052, "y": 948}]}}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1093, "y": 936}, {"x": 1158, "y": 936}, {"x": 1158, "y": 948}, {"x": 1093, "y": 948}]}}]}, {"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 33, "y": 977}, {"x": 54, "y": 977}, {"x": 54, "y": 986}, {"x": 33, "y": 986}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 33, "y": 977}, {"x": 54, "y": 977}, {"x": 54, "y": 986}, {"x": 33, "y": 986}]}}]}, {"text": "Standard of reference", "bounding_poly": {"vertices": [{"x": 94, "y": 977}, {"x": 209, "y": 977}, {"x": 209, "y": 986}, {"x": 94, "y": 986}]}, "elements": [{"text": "Standard", "bounding_poly": {"vertices": [{"x": 94, "y": 977}, {"x": 142, "y": 977}, {"x": 142, "y": 986}, {"x": 94, "y": 986}]}}, {"text": "of", "bounding_poly": {"vertices": [{"x": 146, "y": 977}, {"x": 157, "y": 977}, {"x": 157, "y": 986}, {"x": 146, "y": 986}]}}, {"text": "reference", "bounding_poly": {"vertices": [{"x": 159, "y": 977}, {"x": 209, "y": 977}, {"x": 209, "y": 986}, {"x": 159, "y": 986}]}}]}, {"text": "1", "bounding_poly": {"vertices": [{"x": 32, "y": 999}, {"x": 38, "y": 999}, {"x": 38, "y": 1007}, {"x": 32, "y": 1007}]}, "elements": [{"text": "1", "bounding_poly": {"vertices": [{"x": 32, "y": 999}, {"x": 38, "y": 999}, {"x": 38, "y": 1007}, {"x": 32, "y": 1007}]}}]}, {"text": "EN10160", "bounding_poly": {"vertices": [{"x": 94, "y": 999}, {"x": 143, "y": 999}, {"x": 143, "y": 1008}, {"x": 94, "y": 1008}]}, "elements": [{"text": "EN10160", "bounding_poly": {"vertices": [{"x": 94, "y": 999}, {"x": 143, "y": 999}, {"x": 143, "y": 1008}, {"x": 94, "y": 1008}]}}]}, {"text": "2", "bounding_poly": {"vertices": [{"x": 31, "y": 1018}, {"x": 38, "y": 1018}, {"x": 38, "y": 1025}, {"x": 31, "y": 1025}]}, "elements": [{"text": "2", "bounding_poly": {"vertices": [{"x": 31, "y": 1018}, {"x": 38, "y": 1018}, {"x": 38, "y": 1025}, {"x": 31, "y": 1025}]}}]}, {"text": "EN10160", "bounding_poly": {"vertices": [{"x": 95, "y": 1017}, {"x": 144, "y": 1017}, {"x": 144, "y": 1026}, {"x": 95, "y": 1026}]}, "elements": [{"text": "EN10160", "bounding_poly": {"vertices": [{"x": 95, "y": 1017}, {"x": 144, "y": 1017}, {"x": 144, "y": 1026}, {"x": 95, "y": 1026}]}}]}, {"text": "Acceptance criteria CL.S1 E1 CL.S1 E1", "bounding_poly": {"vertices": [{"x": 235, "y": 977}, {"x": 337, "y": 975}, {"x": 338, "y": 1024}, {"x": 236, "y": 1026}]}, "elements": [{"text": "Acceptance", "bounding_poly": {"vertices": [{"x": 235, "y": 977}, {"x": 297, "y": 976}, {"x": 297, "y": 988}, {"x": 235, "y": 989}]}}, {"text": "criteria", "bounding_poly": {"vertices": [{"x": 301, "y": 976}, {"x": 337, "y": 975}, {"x": 337, "y": 986}, {"x": 301, "y": 987}]}}, {"text": "CL.S1", "bounding_poly": {"vertices": [{"x": 236, "y": 997}, {"x": 266, "y": 997}, {"x": 266, "y": 1009}, {"x": 236, "y": 1009}]}}, {"text": "E1", "bounding_poly": {"vertices": [{"x": 272, "y": 997}, {"x": 284, "y": 997}, {"x": 284, "y": 1008}, {"x": 272, "y": 1008}]}}, {"text": "CL.S1", "bounding_poly": {"vertices": [{"x": 236, "y": 1015}, {"x": 266, "y": 1015}, {"x": 266, "y": 1026}, {"x": 236, "y": 1026}]}}, {"text": "E1", "bounding_poly": {"vertices": [{"x": 272, "y": 1015}, {"x": 284, "y": 1015}, {"x": 284, "y": 1025}, {"x": 272, "y": 1025}]}}]}, {"text": "Result", "bounding_poly": {"vertices": [{"x": 378, "y": 977}, {"x": 413, "y": 977}, {"x": 413, "y": 986}, {"x": 378, "y": 986}]}, "elements": [{"text": "Result", "bounding_poly": {"vertices": [{"x": 378, "y": 977}, {"x": 413, "y": 977}, {"x": 413, "y": 986}, {"x": 378, "y": 986}]}}]}, {"text": "Indications", "bounding_poly": {"vertices": [{"x": 446, "y": 976}, {"x": 503, "y": 977}, {"x": 503, "y": 986}, {"x": 446, "y": 985}]}, "elements": [{"text": "Indications", "bounding_poly": {"vertices": [{"x": 446, "y": 976}, {"x": 503, "y": 977}, {"x": 503, "y": 986}, {"x": 446, "y": 985}]}}]}, {"text": "Ok", "bounding_poly": {"vertices": [{"x": 378, "y": 1000}, {"x": 393, "y": 1000}, {"x": 393, "y": 1008}, {"x": 378, "y": 1008}]}, "elements": [{"text": "Ok", "bounding_poly": {"vertices": [{"x": 378, "y": 1000}, {"x": 393, "y": 1000}, {"x": 393, "y": 1008}, {"x": 378, "y": 1008}]}}]}, {"text": "Ok", "bounding_poly": {"vertices": [{"x": 378, "y": 1018}, {"x": 393, "y": 1018}, {"x": 393, "y": 1026}, {"x": 378, "y": 1026}]}, "elements": [{"text": "Ok", "bounding_poly": {"vertices": [{"x": 378, "y": 1018}, {"x": 393, "y": 1018}, {"x": 393, "y": 1026}, {"x": 378, "y": 1026}]}}]}, {"text": "Not founded Not founded", "bounding_poly": {"vertices": [{"x": 446, "y": 999}, {"x": 511, "y": 999}, {"x": 511, "y": 1026}, {"x": 446, "y": 1026}]}, "elements": [{"text": "Not", "bounding_poly": {"vertices": [{"x": 446, "y": 999}, {"x": 466, "y": 999}, {"x": 466, "y": 1008}, {"x": 446, "y": 1008}]}}, {"text": "founded", "bounding_poly": {"vertices": [{"x": 468, "y": 999}, {"x": 511, "y": 999}, {"x": 511, "y": 1008}, {"x": 468, "y": 1008}]}}, {"text": "Not", "bounding_poly": {"vertices": [{"x": 446, "y": 1016}, {"x": 464, "y": 1016}, {"x": 464, "y": 1026}, {"x": 446, "y": 1026}]}}, {"text": "founded", "bounding_poly": {"vertices": [{"x": 466, "y": 1016}, {"x": 509, "y": 1016}, {"x": 509, "y": 1025}, {"x": 466, "y": 1025}]}}]}, {"text": "Reticle 200x200mm 200x200mm", "bounding_poly": {"vertices": [{"x": 548, "y": 977}, {"x": 613, "y": 977}, {"x": 613, "y": 1026}, {"x": 548, "y": 1026}]}, "elements": [{"text": "Reticle", "bounding_poly": {"vertices": [{"x": 548, "y": 977}, {"x": 584, "y": 977}, {"x": 584, "y": 986}, {"x": 548, "y": 986}]}}, {"text": "200x200mm", "bounding_poly": {"vertices": [{"x": 548, "y": 999}, {"x": 613, "y": 999}, {"x": 613, "y": 1008}, {"x": 548, "y": 1008}]}}, {"text": "200x200mm", "bounding_poly": {"vertices": [{"x": 548, "y": 1017}, {"x": 612, "y": 1017}, {"x": 612, "y": 1026}, {"x": 548, "y": 1026}]}}]}, {"text": "Edges 50 50", "bounding_poly": {"vertices": [{"x": 650, "y": 977}, {"x": 684, "y": 978}, {"x": 683, "y": 1026}, {"x": 649, "y": 1025}]}, "elements": [{"text": "<PERSON>s", "bounding_poly": {"vertices": [{"x": 651, "y": 977}, {"x": 684, "y": 977}, {"x": 684, "y": 987}, {"x": 651, "y": 987}]}}, {"text": "50", "bounding_poly": {"vertices": [{"x": 650, "y": 1000}, {"x": 663, "y": 1000}, {"x": 663, "y": 1008}, {"x": 650, "y": 1008}]}}, {"text": "50", "bounding_poly": {"vertices": [{"x": 650, "y": 1017}, {"x": 663, "y": 1017}, {"x": 663, "y": 1025}, {"x": 650, "y": 1025}]}}]}, {"text": "Probe BDD20 / 4 BDD20 / 4", "bounding_poly": {"vertices": [{"x": 752, "y": 976}, {"x": 800, "y": 976}, {"x": 800, "y": 1026}, {"x": 752, "y": 1026}]}, "elements": [{"text": "Probe", "bounding_poly": {"vertices": [{"x": 752, "y": 976}, {"x": 782, "y": 976}, {"x": 782, "y": 986}, {"x": 752, "y": 986}]}}, {"text": "BDD20", "bounding_poly": {"vertices": [{"x": 753, "y": 999}, {"x": 789, "y": 999}, {"x": 789, "y": 1008}, {"x": 753, "y": 1008}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 790, "y": 999}, {"x": 794, "y": 999}, {"x": 794, "y": 1008}, {"x": 790, "y": 1008}]}}, {"text": "4", "bounding_poly": {"vertices": [{"x": 793, "y": 999}, {"x": 800, "y": 999}, {"x": 800, "y": 1008}, {"x": 793, "y": 1008}]}}, {"text": "BDD20", "bounding_poly": {"vertices": [{"x": 752, "y": 1016}, {"x": 788, "y": 1016}, {"x": 788, "y": 1026}, {"x": 752, "y": 1026}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 790, "y": 1016}, {"x": 794, "y": 1016}, {"x": 794, "y": 1025}, {"x": 790, "y": 1025}]}}, {"text": "4", "bounding_poly": {"vertices": [{"x": 792, "y": 1016}, {"x": 799, "y": 1016}, {"x": 799, "y": 1025}, {"x": 792, "y": 1025}]}}]}, {"text": "Frequency", "bounding_poly": {"vertices": [{"x": 854, "y": 976}, {"x": 909, "y": 975}, {"x": 909, "y": 988}, {"x": 854, "y": 989}]}, "elements": [{"text": "Frequency", "bounding_poly": {"vertices": [{"x": 854, "y": 976}, {"x": 909, "y": 975}, {"x": 909, "y": 988}, {"x": 854, "y": 989}]}}]}, {"text": "4 MHz 4 MHz", "bounding_poly": {"vertices": [{"x": 854, "y": 1000}, {"x": 888, "y": 1000}, {"x": 888, "y": 1025}, {"x": 854, "y": 1025}]}, "elements": [{"text": "4", "bounding_poly": {"vertices": [{"x": 854, "y": 1000}, {"x": 861, "y": 1000}, {"x": 861, "y": 1008}, {"x": 854, "y": 1008}]}}, {"text": "MHz", "bounding_poly": {"vertices": [{"x": 864, "y": 1000}, {"x": 888, "y": 1000}, {"x": 888, "y": 1008}, {"x": 864, "y": 1008}]}}, {"text": "4", "bounding_poly": {"vertices": [{"x": 854, "y": 1018}, {"x": 861, "y": 1018}, {"x": 861, "y": 1025}, {"x": 854, "y": 1025}]}}, {"text": "MHz", "bounding_poly": {"vertices": [{"x": 863, "y": 1018}, {"x": 888, "y": 1018}, {"x": 888, "y": 1025}, {"x": 863, "y": 1025}]}}]}, {"text": "Angle 0 ° 0 °", "bounding_poly": {"vertices": [{"x": 955, "y": 977}, {"x": 986, "y": 977}, {"x": 986, "y": 1025}, {"x": 955, "y": 1025}]}, "elements": [{"text": "<PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 955, "y": 977}, {"x": 986, "y": 977}, {"x": 986, "y": 987}, {"x": 955, "y": 987}]}}, {"text": "0", "bounding_poly": {"vertices": [{"x": 956, "y": 999}, {"x": 963, "y": 999}, {"x": 963, "y": 1007}, {"x": 956, "y": 1007}]}}, {"text": "°", "bounding_poly": {"vertices": [{"x": 963, "y": 999}, {"x": 968, "y": 999}, {"x": 968, "y": 1007}, {"x": 963, "y": 1007}]}}, {"text": "0", "bounding_poly": {"vertices": [{"x": 956, "y": 1017}, {"x": 963, "y": 1017}, {"x": 963, "y": 1025}, {"x": 956, "y": 1025}]}}, {"text": "°", "bounding_poly": {"vertices": [{"x": 963, "y": 1017}, {"x": 968, "y": 1017}, {"x": 968, "y": 1025}, {"x": 963, "y": 1025}]}}]}, {"text": "Material Free of radioactive contamination", "bounding_poly": {"vertices": [{"x": 39, "y": 1086}, {"x": 262, "y": 1087}, {"x": 262, "y": 1098}, {"x": 39, "y": 1097}]}, "elements": [{"text": "Material", "bounding_poly": {"vertices": [{"x": 39, "y": 1087}, {"x": 80, "y": 1087}, {"x": 80, "y": 1097}, {"x": 39, "y": 1097}]}}, {"text": "Free", "bounding_poly": {"vertices": [{"x": 85, "y": 1087}, {"x": 109, "y": 1087}, {"x": 109, "y": 1097}, {"x": 85, "y": 1097}]}}, {"text": "of", "bounding_poly": {"vertices": [{"x": 114, "y": 1087}, {"x": 123, "y": 1087}, {"x": 123, "y": 1097}, {"x": 114, "y": 1097}]}}, {"text": "radioactive", "bounding_poly": {"vertices": [{"x": 127, "y": 1087}, {"x": 184, "y": 1087}, {"x": 184, "y": 1097}, {"x": 127, "y": 1097}]}}, {"text": "contamination", "bounding_poly": {"vertices": [{"x": 188, "y": 1087}, {"x": 262, "y": 1087}, {"x": 262, "y": 1097}, {"x": 188, "y": 1097}]}}]}, {"text": "Dimensional check and visual examination of the surface condition : without objection \nWe Declare that the above mentioned plates are in compliance with the order prescription \n-We hereby declare that the above mentioned material have been rolled in Italy , thus of European Community origin", "bounding_poly": {"vertices": [{"x": 33, "y": 1105}, {"x": 649, "y": 1105}, {"x": 649, "y": 1153}, {"x": 33, "y": 1153}]}, "elements": [{"text": "Dimensional", "bounding_poly": {"vertices": [{"x": 40, "y": 1105}, {"x": 105, "y": 1105}, {"x": 105, "y": 1116}, {"x": 40, "y": 1116}]}}, {"text": "check", "bounding_poly": {"vertices": [{"x": 109, "y": 1105}, {"x": 141, "y": 1105}, {"x": 141, "y": 1116}, {"x": 109, "y": 1116}]}}, {"text": "and", "bounding_poly": {"vertices": [{"x": 143, "y": 1105}, {"x": 163, "y": 1105}, {"x": 163, "y": 1116}, {"x": 143, "y": 1116}]}}, {"text": "visual", "bounding_poly": {"vertices": [{"x": 166, "y": 1105}, {"x": 198, "y": 1105}, {"x": 198, "y": 1116}, {"x": 166, "y": 1116}]}}, {"text": "examination", "bounding_poly": {"vertices": [{"x": 200, "y": 1105}, {"x": 265, "y": 1105}, {"x": 265, "y": 1116}, {"x": 200, "y": 1116}]}}, {"text": "of", "bounding_poly": {"vertices": [{"x": 268, "y": 1105}, {"x": 280, "y": 1105}, {"x": 280, "y": 1116}, {"x": 268, "y": 1116}]}}, {"text": "the", "bounding_poly": {"vertices": [{"x": 281, "y": 1105}, {"x": 298, "y": 1105}, {"x": 298, "y": 1116}, {"x": 281, "y": 1116}]}}, {"text": "surface", "bounding_poly": {"vertices": [{"x": 302, "y": 1105}, {"x": 341, "y": 1105}, {"x": 341, "y": 1116}, {"x": 302, "y": 1116}]}}, {"text": "condition", "bounding_poly": {"vertices": [{"x": 344, "y": 1105}, {"x": 393, "y": 1105}, {"x": 393, "y": 1116}, {"x": 344, "y": 1116}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 393, "y": 1105}, {"x": 397, "y": 1105}, {"x": 397, "y": 1116}, {"x": 393, "y": 1116}]}}, {"text": "without", "bounding_poly": {"vertices": [{"x": 399, "y": 1105}, {"x": 439, "y": 1105}, {"x": 439, "y": 1116}, {"x": 399, "y": 1116}]}}, {"text": "objection", "bounding_poly": {"vertices": [{"x": 441, "y": 1105}, {"x": 488, "y": 1105}, {"x": 488, "y": 1116}, {"x": 441, "y": 1116}]}}, {"text": "We", "bounding_poly": {"vertices": [{"x": 38, "y": 1123}, {"x": 57, "y": 1123}, {"x": 57, "y": 1134}, {"x": 38, "y": 1134}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 61, "y": 1123}, {"x": 101, "y": 1123}, {"x": 101, "y": 1134}, {"x": 61, "y": 1134}]}}, {"text": "that", "bounding_poly": {"vertices": [{"x": 105, "y": 1123}, {"x": 126, "y": 1123}, {"x": 126, "y": 1134}, {"x": 105, "y": 1134}]}}, {"text": "the", "bounding_poly": {"vertices": [{"x": 128, "y": 1123}, {"x": 145, "y": 1123}, {"x": 145, "y": 1134}, {"x": 128, "y": 1134}]}}, {"text": "above", "bounding_poly": {"vertices": [{"x": 149, "y": 1123}, {"x": 182, "y": 1123}, {"x": 182, "y": 1134}, {"x": 149, "y": 1134}]}}, {"text": "mentioned", "bounding_poly": {"vertices": [{"x": 185, "y": 1123}, {"x": 240, "y": 1123}, {"x": 240, "y": 1134}, {"x": 185, "y": 1134}]}}, {"text": "plates", "bounding_poly": {"vertices": [{"x": 244, "y": 1123}, {"x": 276, "y": 1123}, {"x": 276, "y": 1134}, {"x": 244, "y": 1134}]}}, {"text": "are", "bounding_poly": {"vertices": [{"x": 279, "y": 1123}, {"x": 297, "y": 1123}, {"x": 297, "y": 1134}, {"x": 279, "y": 1134}]}}, {"text": "in", "bounding_poly": {"vertices": [{"x": 300, "y": 1123}, {"x": 309, "y": 1123}, {"x": 309, "y": 1134}, {"x": 300, "y": 1134}]}}, {"text": "compliance", "bounding_poly": {"vertices": [{"x": 313, "y": 1123}, {"x": 374, "y": 1123}, {"x": 374, "y": 1134}, {"x": 313, "y": 1134}]}}, {"text": "with", "bounding_poly": {"vertices": [{"x": 376, "y": 1123}, {"x": 398, "y": 1123}, {"x": 398, "y": 1134}, {"x": 376, "y": 1134}]}}, {"text": "the", "bounding_poly": {"vertices": [{"x": 400, "y": 1123}, {"x": 418, "y": 1123}, {"x": 418, "y": 1134}, {"x": 400, "y": 1134}]}}, {"text": "order", "bounding_poly": {"vertices": [{"x": 421, "y": 1123}, {"x": 450, "y": 1123}, {"x": 450, "y": 1134}, {"x": 421, "y": 1134}]}}, {"text": "prescription", "bounding_poly": {"vertices": [{"x": 452, "y": 1123}, {"x": 514, "y": 1123}, {"x": 514, "y": 1134}, {"x": 452, "y": 1134}]}}, {"text": "-We", "bounding_poly": {"vertices": [{"x": 33, "y": 1141}, {"x": 57, "y": 1141}, {"x": 57, "y": 1153}, {"x": 33, "y": 1153}]}}, {"text": "hereby", "bounding_poly": {"vertices": [{"x": 60, "y": 1141}, {"x": 98, "y": 1141}, {"x": 98, "y": 1153}, {"x": 60, "y": 1153}]}}, {"text": "declare", "bounding_poly": {"vertices": [{"x": 100, "y": 1141}, {"x": 140, "y": 1141}, {"x": 140, "y": 1153}, {"x": 100, "y": 1153}]}}, {"text": "that", "bounding_poly": {"vertices": [{"x": 143, "y": 1141}, {"x": 164, "y": 1141}, {"x": 164, "y": 1153}, {"x": 143, "y": 1153}]}}, {"text": "the", "bounding_poly": {"vertices": [{"x": 166, "y": 1141}, {"x": 183, "y": 1141}, {"x": 183, "y": 1153}, {"x": 166, "y": 1153}]}}, {"text": "above", "bounding_poly": {"vertices": [{"x": 186, "y": 1141}, {"x": 220, "y": 1141}, {"x": 220, "y": 1153}, {"x": 186, "y": 1153}]}}, {"text": "mentioned", "bounding_poly": {"vertices": [{"x": 223, "y": 1141}, {"x": 278, "y": 1141}, {"x": 278, "y": 1153}, {"x": 223, "y": 1153}]}}, {"text": "material", "bounding_poly": {"vertices": [{"x": 282, "y": 1141}, {"x": 325, "y": 1141}, {"x": 325, "y": 1153}, {"x": 282, "y": 1153}]}}, {"text": "have", "bounding_poly": {"vertices": [{"x": 328, "y": 1141}, {"x": 354, "y": 1141}, {"x": 354, "y": 1153}, {"x": 328, "y": 1153}]}}, {"text": "been", "bounding_poly": {"vertices": [{"x": 357, "y": 1141}, {"x": 384, "y": 1141}, {"x": 384, "y": 1153}, {"x": 357, "y": 1153}]}}, {"text": "rolled", "bounding_poly": {"vertices": [{"x": 388, "y": 1141}, {"x": 416, "y": 1141}, {"x": 416, "y": 1153}, {"x": 388, "y": 1153}]}}, {"text": "in", "bounding_poly": {"vertices": [{"x": 420, "y": 1141}, {"x": 429, "y": 1141}, {"x": 429, "y": 1153}, {"x": 420, "y": 1153}]}}, {"text": "Italy", "bounding_poly": {"vertices": [{"x": 433, "y": 1141}, {"x": 455, "y": 1141}, {"x": 455, "y": 1153}, {"x": 433, "y": 1153}]}}, {"text": ",", "bounding_poly": {"vertices": [{"x": 455, "y": 1141}, {"x": 458, "y": 1141}, {"x": 458, "y": 1153}, {"x": 455, "y": 1153}]}}, {"text": "thus", "bounding_poly": {"vertices": [{"x": 461, "y": 1141}, {"x": 483, "y": 1141}, {"x": 483, "y": 1153}, {"x": 461, "y": 1153}]}}, {"text": "of", "bounding_poly": {"vertices": [{"x": 487, "y": 1141}, {"x": 498, "y": 1141}, {"x": 498, "y": 1153}, {"x": 487, "y": 1153}]}}, {"text": "European", "bounding_poly": {"vertices": [{"x": 501, "y": 1141}, {"x": 552, "y": 1141}, {"x": 552, "y": 1153}, {"x": 501, "y": 1153}]}}, {"text": "Community", "bounding_poly": {"vertices": [{"x": 556, "y": 1141}, {"x": 618, "y": 1141}, {"x": 618, "y": 1153}, {"x": 556, "y": 1153}]}}, {"text": "origin", "bounding_poly": {"vertices": [{"x": 620, "y": 1141}, {"x": 649, "y": 1141}, {"x": 649, "y": 1153}, {"x": 620, "y": 1153}]}}]}, {"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1342, "y": 921}, {"x": 1384, "y": 920}, {"x": 1384, "y": 934}, {"x": 1342, "y": 935}]}, "elements": [{"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1342, "y": 921}, {"x": 1384, "y": 920}, {"x": 1384, "y": 934}, {"x": 1342, "y": 935}]}}]}, {"text": "MARCEGAGLIA <PERSON>", "bounding_poly": {"vertices": [{"x": 1218, "y": 893}, {"x": 1385, "y": 892}, {"x": 1385, "y": 962}, {"x": 1218, "y": 963}]}, "elements": [{"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 1246, "y": 895}, {"x": 1385, "y": 893}, {"x": 1385, "y": 918}, {"x": 1246, "y": 920}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1218, "y": 928}, {"x": 1280, "y": 928}, {"x": 1280, "y": 961}, {"x": 1218, "y": 961}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1279, "y": 928}, {"x": 1351, "y": 928}, {"x": 1351, "y": 961}, {"x": 1279, "y": 961}]}}]}, {"text": "( 1 ) Location : 1 - Top : 2 - Bottom ( 2 ) Location : 1 / 2-1 / 2 thickness ; P - sourface : 1 / 4-1 / 4 thickness . ( 3 ) Direction : L - Longitudinal ; T - Trasverse : ( 4 ) Specimen Thickness", "bounding_poly": {"vertices": [{"x": 1052, "y": 970}, {"x": 1599, "y": 970}, {"x": 1599, "y": 978}, {"x": 1052, "y": 978}]}, "elements": [{"text": "(", "bounding_poly": {"vertices": [{"x": 1052, "y": 970}, {"x": 1055, "y": 970}, {"x": 1055, "y": 978}, {"x": 1052, "y": 978}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 1055, "y": 970}, {"x": 1058, "y": 970}, {"x": 1058, "y": 978}, {"x": 1055, "y": 978}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1058, "y": 970}, {"x": 1061, "y": 970}, {"x": 1061, "y": 978}, {"x": 1058, "y": 978}]}}, {"text": "Location", "bounding_poly": {"vertices": [{"x": 1062, "y": 970}, {"x": 1092, "y": 970}, {"x": 1092, "y": 978}, {"x": 1062, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1092, "y": 970}, {"x": 1094, "y": 970}, {"x": 1094, "y": 978}, {"x": 1092, "y": 978}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 1094, "y": 970}, {"x": 1097, "y": 970}, {"x": 1097, "y": 978}, {"x": 1094, "y": 978}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1099, "y": 970}, {"x": 1103, "y": 970}, {"x": 1103, "y": 978}, {"x": 1099, "y": 978}]}}, {"text": "Top", "bounding_poly": {"vertices": [{"x": 1103, "y": 970}, {"x": 1117, "y": 970}, {"x": 1117, "y": 978}, {"x": 1103, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1116, "y": 970}, {"x": 1119, "y": 970}, {"x": 1119, "y": 978}, {"x": 1116, "y": 978}]}}, {"text": "2", "bounding_poly": {"vertices": [{"x": 1119, "y": 970}, {"x": 1124, "y": 970}, {"x": 1124, "y": 978}, {"x": 1119, "y": 978}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1124, "y": 970}, {"x": 1128, "y": 970}, {"x": 1128, "y": 978}, {"x": 1124, "y": 978}]}}, {"text": "Bottom", "bounding_poly": {"vertices": [{"x": 1128, "y": 970}, {"x": 1152, "y": 970}, {"x": 1152, "y": 978}, {"x": 1128, "y": 978}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 1156, "y": 970}, {"x": 1159, "y": 970}, {"x": 1159, "y": 978}, {"x": 1156, "y": 978}]}}, {"text": "2", "bounding_poly": {"vertices": [{"x": 1159, "y": 970}, {"x": 1163, "y": 970}, {"x": 1163, "y": 978}, {"x": 1159, "y": 978}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1163, "y": 970}, {"x": 1166, "y": 970}, {"x": 1166, "y": 978}, {"x": 1163, "y": 978}]}}, {"text": "Location", "bounding_poly": {"vertices": [{"x": 1166, "y": 970}, {"x": 1196, "y": 970}, {"x": 1196, "y": 978}, {"x": 1166, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1196, "y": 970}, {"x": 1198, "y": 970}, {"x": 1198, "y": 978}, {"x": 1196, "y": 978}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 1198, "y": 970}, {"x": 1201, "y": 970}, {"x": 1201, "y": 978}, {"x": 1198, "y": 978}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1202, "y": 970}, {"x": 1205, "y": 970}, {"x": 1205, "y": 978}, {"x": 1202, "y": 978}]}}, {"text": "2-1", "bounding_poly": {"vertices": [{"x": 1204, "y": 970}, {"x": 1218, "y": 970}, {"x": 1218, "y": 978}, {"x": 1204, "y": 978}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1218, "y": 970}, {"x": 1221, "y": 970}, {"x": 1221, "y": 978}, {"x": 1218, "y": 978}]}}, {"text": "2", "bounding_poly": {"vertices": [{"x": 1220, "y": 970}, {"x": 1225, "y": 970}, {"x": 1225, "y": 978}, {"x": 1220, "y": 978}]}}, {"text": "thickness", "bounding_poly": {"vertices": [{"x": 1227, "y": 970}, {"x": 1260, "y": 970}, {"x": 1260, "y": 978}, {"x": 1227, "y": 978}]}}, {"text": ";", "bounding_poly": {"vertices": [{"x": 1261, "y": 970}, {"x": 1264, "y": 970}, {"x": 1264, "y": 978}, {"x": 1261, "y": 978}]}}, {"text": "P", "bounding_poly": {"vertices": [{"x": 1263, "y": 970}, {"x": 1268, "y": 970}, {"x": 1268, "y": 978}, {"x": 1263, "y": 978}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1268, "y": 970}, {"x": 1272, "y": 970}, {"x": 1272, "y": 978}, {"x": 1268, "y": 978}]}}, {"text": "sourface", "bounding_poly": {"vertices": [{"x": 1272, "y": 970}, {"x": 1303, "y": 970}, {"x": 1303, "y": 978}, {"x": 1272, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1303, "y": 970}, {"x": 1305, "y": 970}, {"x": 1305, "y": 978}, {"x": 1303, "y": 978}]}}, {"text": "1", "bounding_poly": {"vertices": [{"x": 1306, "y": 970}, {"x": 1310, "y": 970}, {"x": 1310, "y": 978}, {"x": 1306, "y": 978}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1310, "y": 970}, {"x": 1313, "y": 970}, {"x": 1313, "y": 978}, {"x": 1310, "y": 978}]}}, {"text": "4-1", "bounding_poly": {"vertices": [{"x": 1312, "y": 970}, {"x": 1326, "y": 970}, {"x": 1326, "y": 978}, {"x": 1312, "y": 978}]}}, {"text": "/", "bounding_poly": {"vertices": [{"x": 1326, "y": 970}, {"x": 1329, "y": 970}, {"x": 1329, "y": 978}, {"x": 1326, "y": 978}]}}, {"text": "4", "bounding_poly": {"vertices": [{"x": 1328, "y": 970}, {"x": 1333, "y": 970}, {"x": 1333, "y": 978}, {"x": 1328, "y": 978}]}}, {"text": "thickness", "bounding_poly": {"vertices": [{"x": 1335, "y": 970}, {"x": 1368, "y": 970}, {"x": 1368, "y": 978}, {"x": 1335, "y": 978}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 1368, "y": 970}, {"x": 1370, "y": 970}, {"x": 1370, "y": 978}, {"x": 1368, "y": 978}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 1371, "y": 970}, {"x": 1373, "y": 970}, {"x": 1373, "y": 978}, {"x": 1371, "y": 978}]}}, {"text": "3", "bounding_poly": {"vertices": [{"x": 1373, "y": 970}, {"x": 1377, "y": 970}, {"x": 1377, "y": 978}, {"x": 1373, "y": 978}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1377, "y": 970}, {"x": 1380, "y": 970}, {"x": 1380, "y": 978}, {"x": 1377, "y": 978}]}}, {"text": "Direction", "bounding_poly": {"vertices": [{"x": 1380, "y": 970}, {"x": 1412, "y": 970}, {"x": 1412, "y": 978}, {"x": 1380, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1411, "y": 970}, {"x": 1414, "y": 970}, {"x": 1414, "y": 978}, {"x": 1411, "y": 978}]}}, {"text": "L", "bounding_poly": {"vertices": [{"x": 1414, "y": 970}, {"x": 1419, "y": 970}, {"x": 1419, "y": 978}, {"x": 1414, "y": 978}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1419, "y": 970}, {"x": 1423, "y": 970}, {"x": 1423, "y": 978}, {"x": 1419, "y": 978}]}}, {"text": "Longitudinal", "bounding_poly": {"vertices": [{"x": 1423, "y": 970}, {"x": 1466, "y": 970}, {"x": 1466, "y": 978}, {"x": 1423, "y": 978}]}}, {"text": ";", "bounding_poly": {"vertices": [{"x": 1466, "y": 970}, {"x": 1468, "y": 970}, {"x": 1468, "y": 978}, {"x": 1466, "y": 978}]}}, {"text": "T", "bounding_poly": {"vertices": [{"x": 1469, "y": 970}, {"x": 1474, "y": 970}, {"x": 1474, "y": 978}, {"x": 1469, "y": 978}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1474, "y": 970}, {"x": 1479, "y": 970}, {"x": 1479, "y": 978}, {"x": 1474, "y": 978}]}}, {"text": "Trasverse", "bounding_poly": {"vertices": [{"x": 1478, "y": 970}, {"x": 1513, "y": 970}, {"x": 1513, "y": 978}, {"x": 1478, "y": 978}]}}, {"text": ":", "bounding_poly": {"vertices": [{"x": 1514, "y": 970}, {"x": 1516, "y": 970}, {"x": 1516, "y": 978}, {"x": 1514, "y": 978}]}}, {"text": "(", "bounding_poly": {"vertices": [{"x": 1517, "y": 970}, {"x": 1520, "y": 970}, {"x": 1520, "y": 978}, {"x": 1517, "y": 978}]}}, {"text": "4", "bounding_poly": {"vertices": [{"x": 1518, "y": 970}, {"x": 1523, "y": 970}, {"x": 1523, "y": 978}, {"x": 1518, "y": 978}]}}, {"text": ")", "bounding_poly": {"vertices": [{"x": 1523, "y": 970}, {"x": 1526, "y": 970}, {"x": 1526, "y": 978}, {"x": 1523, "y": 978}]}}, {"text": "Specimen", "bounding_poly": {"vertices": [{"x": 1525, "y": 970}, {"x": 1561, "y": 970}, {"x": 1561, "y": 978}, {"x": 1525, "y": 978}]}}, {"text": "<PERSON><PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1563, "y": 970}, {"x": 1599, "y": 970}, {"x": 1599, "y": 978}, {"x": 1563, "y": 978}]}}]}, {"text": "UK CA", "bounding_poly": {"vertices": [{"x": 1061, "y": 1026}, {"x": 1196, "y": 1027}, {"x": 1195, "y": 1152}, {"x": 1060, "y": 1151}]}, "elements": [{"text": "UK", "bounding_poly": {"vertices": [{"x": 1061, "y": 1027}, {"x": 1196, "y": 1027}, {"x": 1196, "y": 1080}, {"x": 1061, "y": 1080}]}}, {"text": "CA", "bounding_poly": {"vertices": [{"x": 1063, "y": 1094}, {"x": 1190, "y": 1095}, {"x": 1189, "y": 1151}, {"x": 1062, "y": 1150}]}}]}, {"text": "1244", "bounding_poly": {"vertices": [{"x": 1223, "y": 1024}, {"x": 1259, "y": 1024}, {"x": 1259, "y": 1033}, {"x": 1223, "y": 1033}]}, "elements": [{"text": "1244", "bounding_poly": {"vertices": [{"x": 1223, "y": 1024}, {"x": 1259, "y": 1024}, {"x": 1259, "y": 1033}, {"x": 1223, "y": 1033}]}}]}, {"text": "22", "bounding_poly": {"vertices": [{"x": 1234, "y": 1045}, {"x": 1247, "y": 1045}, {"x": 1247, "y": 1051}, {"x": 1234, "y": 1051}]}, "elements": [{"text": "22", "bounding_poly": {"vertices": [{"x": 1234, "y": 1045}, {"x": 1247, "y": 1045}, {"x": 1247, "y": 1051}, {"x": 1234, "y": 1051}]}}]}, {"text": "DescrizioneMarchi", "bounding_poly": {"vertices": [{"x": 1254, "y": 1000}, {"x": 1351, "y": 999}, {"x": 1351, "y": 1010}, {"x": 1254, "y": 1011}]}, "elements": [{"text": "DescrizioneMarchi", "bounding_poly": {"vertices": [{"x": 1254, "y": 1000}, {"x": 1351, "y": 999}, {"x": 1351, "y": 1010}, {"x": 1254, "y": 1011}]}}]}, {"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 1205, "y": 1061}, {"x": 1295, "y": 1061}, {"x": 1295, "y": 1071}, {"x": 1205, "y": 1071}]}, "elements": [{"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 1205, "y": 1061}, {"x": 1295, "y": 1061}, {"x": 1295, "y": 1071}, {"x": 1205, "y": 1071}]}}]}, {"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1266, "y": 1073}, {"x": 1295, "y": 1073}, {"x": 1295, "y": 1080}, {"x": 1266, "y": 1080}]}, "elements": [{"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1266, "y": 1073}, {"x": 1295, "y": 1073}, {"x": 1295, "y": 1080}, {"x": 1266, "y": 1080}]}}]}, {"text": "v . <PERSON> , 33 33058 <PERSON><PERSON> - Italy", "bounding_poly": {"vertices": [{"x": 1204, "y": 1082}, {"x": 1297, "y": 1082}, {"x": 1297, "y": 1108}, {"x": 1204, "y": 1108}]}, "elements": [{"text": "v", "bounding_poly": {"vertices": [{"x": 1204, "y": 1083}, {"x": 1209, "y": 1083}, {"x": 1209, "y": 1090}, {"x": 1204, "y": 1090}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 1208, "y": 1083}, {"x": 1211, "y": 1083}, {"x": 1211, "y": 1090}, {"x": 1208, "y": 1090}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1212, "y": 1083}, {"x": 1236, "y": 1083}, {"x": 1236, "y": 1090}, {"x": 1212, "y": 1090}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1238, "y": 1083}, {"x": 1259, "y": 1083}, {"x": 1259, "y": 1090}, {"x": 1238, "y": 1090}]}}, {"text": ",", "bounding_poly": {"vertices": [{"x": 1259, "y": 1083}, {"x": 1262, "y": 1083}, {"x": 1262, "y": 1090}, {"x": 1259, "y": 1090}]}}, {"text": "33", "bounding_poly": {"vertices": [{"x": 1263, "y": 1083}, {"x": 1272, "y": 1083}, {"x": 1272, "y": 1090}, {"x": 1263, "y": 1090}]}}, {"text": "33058", "bounding_poly": {"vertices": [{"x": 1204, "y": 1092}, {"x": 1224, "y": 1092}, {"x": 1224, "y": 1100}, {"x": 1204, "y": 1100}]}}, {"text": "S.", "bounding_poly": {"vertices": [{"x": 1231, "y": 1092}, {"x": 1240, "y": 1092}, {"x": 1240, "y": 1100}, {"x": 1231, "y": 1100}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1240, "y": 1092}, {"x": 1269, "y": 1092}, {"x": 1269, "y": 1100}, {"x": 1240, "y": 1100}]}}, {"text": "Nogaro", "bounding_poly": {"vertices": [{"x": 1270, "y": 1092}, {"x": 1297, "y": 1092}, {"x": 1297, "y": 1100}, {"x": 1270, "y": 1100}]}}, {"text": "Udine", "bounding_poly": {"vertices": [{"x": 1204, "y": 1100}, {"x": 1224, "y": 1100}, {"x": 1224, "y": 1108}, {"x": 1204, "y": 1108}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1225, "y": 1100}, {"x": 1229, "y": 1100}, {"x": 1229, "y": 1107}, {"x": 1225, "y": 1107}]}}, {"text": "Italy", "bounding_poly": {"vertices": [{"x": 1231, "y": 1100}, {"x": 1246, "y": 1100}, {"x": 1246, "y": 1107}, {"x": 1231, "y": 1107}]}}]}, {"text": "EN 10025-1 \nEN 10025-2", "bounding_poly": {"vertices": [{"x": 1205, "y": 1129}, {"x": 1267, "y": 1129}, {"x": 1267, "y": 1155}, {"x": 1205, "y": 1155}]}, "elements": [{"text": "EN", "bounding_poly": {"vertices": [{"x": 1205, "y": 1130}, {"x": 1218, "y": 1130}, {"x": 1218, "y": 1139}, {"x": 1205, "y": 1139}]}}, {"text": "10025-1", "bounding_poly": {"vertices": [{"x": 1223, "y": 1130}, {"x": 1265, "y": 1129}, {"x": 1265, "y": 1138}, {"x": 1223, "y": 1139}]}}, {"text": "EN", "bounding_poly": {"vertices": [{"x": 1205, "y": 1145}, {"x": 1218, "y": 1145}, {"x": 1218, "y": 1154}, {"x": 1205, "y": 1154}]}}, {"text": "10025-2", "bounding_poly": {"vertices": [{"x": 1223, "y": 1145}, {"x": 1267, "y": 1145}, {"x": 1267, "y": 1154}, {"x": 1223, "y": 1154}]}}]}, {"text": "0474", "bounding_poly": {"vertices": [{"x": 1566, "y": 1028}, {"x": 1600, "y": 1028}, {"x": 1600, "y": 1038}, {"x": 1566, "y": 1038}]}, "elements": [{"text": "0474", "bounding_poly": {"vertices": [{"x": 1566, "y": 1028}, {"x": 1600, "y": 1028}, {"x": 1600, "y": 1038}, {"x": 1566, "y": 1038}]}}]}, {"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 1550, "y": 1063}, {"x": 1636, "y": 1062}, {"x": 1636, "y": 1073}, {"x": 1550, "y": 1074}]}, "elements": [{"text": "MARCEGAGLIA", "bounding_poly": {"vertices": [{"x": 1550, "y": 1063}, {"x": 1636, "y": 1062}, {"x": 1636, "y": 1073}, {"x": 1550, "y": 1074}]}}]}, {"text": "v . <PERSON> , 33", "bounding_poly": {"vertices": [{"x": 1548, "y": 1084}, {"x": 1615, "y": 1085}, {"x": 1615, "y": 1092}, {"x": 1548, "y": 1091}]}, "elements": [{"text": "v", "bounding_poly": {"vertices": [{"x": 1548, "y": 1084}, {"x": 1554, "y": 1084}, {"x": 1554, "y": 1091}, {"x": 1548, "y": 1091}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 1552, "y": 1084}, {"x": 1555, "y": 1084}, {"x": 1555, "y": 1091}, {"x": 1552, "y": 1091}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1556, "y": 1084}, {"x": 1580, "y": 1084}, {"x": 1580, "y": 1091}, {"x": 1556, "y": 1091}]}}, {"text": "<PERSON><PERSON><PERSON>", "bounding_poly": {"vertices": [{"x": 1581, "y": 1084}, {"x": 1601, "y": 1084}, {"x": 1601, "y": 1091}, {"x": 1581, "y": 1091}]}}, {"text": ",", "bounding_poly": {"vertices": [{"x": 1602, "y": 1084}, {"x": 1605, "y": 1084}, {"x": 1605, "y": 1091}, {"x": 1602, "y": 1091}]}}, {"text": "33", "bounding_poly": {"vertices": [{"x": 1605, "y": 1084}, {"x": 1615, "y": 1084}, {"x": 1615, "y": 1091}, {"x": 1605, "y": 1091}]}}]}, {"text": "CE", "bounding_poly": {"vertices": [{"x": 1360, "y": 1022}, {"x": 1559, "y": 1022}, {"x": 1559, "y": 1154}, {"x": 1360, "y": 1154}]}, "elements": [{"text": "CE", "bounding_poly": {"vertices": [{"x": 1360, "y": 1022}, {"x": 1559, "y": 1022}, {"x": 1559, "y": 1154}, {"x": 1360, "y": 1154}]}}]}, {"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1608, "y": 1076}, {"x": 1637, "y": 1076}, {"x": 1637, "y": 1081}, {"x": 1608, "y": 1081}]}, "elements": [{"text": "PLATES", "bounding_poly": {"vertices": [{"x": 1608, "y": 1076}, {"x": 1637, "y": 1076}, {"x": 1637, "y": 1081}, {"x": 1608, "y": 1081}]}}]}, {"text": "nio <PERSON>", "bounding_poly": {"vertices": [{"x": 1600, "y": 1088}, {"x": 1637, "y": 1093}, {"x": 1635, "y": 1102}, {"x": 1599, "y": 1097}]}, "elements": [{"text": "nio", "bounding_poly": {"vertices": [{"x": 1600, "y": 1088}, {"x": 1611, "y": 1090}, {"x": 1610, "y": 1098}, {"x": 1599, "y": 1097}]}}, {"text": "Nogaro", "bounding_poly": {"vertices": [{"x": 1612, "y": 1090}, {"x": 1637, "y": 1094}, {"x": 1635, "y": 1102}, {"x": 1611, "y": 1099}]}}]}, {"text": "33058 - <PERSON> <PERSON> - Italy", "bounding_poly": {"vertices": [{"x": 1549, "y": 1092}, {"x": 1610, "y": 1091}, {"x": 1610, "y": 1109}, {"x": 1549, "y": 1110}]}, "elements": [{"text": "33058", "bounding_poly": {"vertices": [{"x": 1549, "y": 1092}, {"x": 1570, "y": 1092}, {"x": 1570, "y": 1101}, {"x": 1549, "y": 1101}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1571, "y": 1092}, {"x": 1575, "y": 1092}, {"x": 1575, "y": 1100}, {"x": 1571, "y": 1100}]}}, {"text": "S", "bounding_poly": {"vertices": [{"x": 1575, "y": 1092}, {"x": 1581, "y": 1092}, {"x": 1581, "y": 1100}, {"x": 1575, "y": 1100}]}}, {"text": ".", "bounding_poly": {"vertices": [{"x": 1579, "y": 1092}, {"x": 1582, "y": 1092}, {"x": 1582, "y": 1100}, {"x": 1579, "y": 1100}]}}, {"text": "<PERSON>", "bounding_poly": {"vertices": [{"x": 1583, "y": 1092}, {"x": 1610, "y": 1092}, {"x": 1610, "y": 1100}, {"x": 1583, "y": 1100}]}}, {"text": "Udine", "bounding_poly": {"vertices": [{"x": 1549, "y": 1102}, {"x": 1568, "y": 1102}, {"x": 1568, "y": 1110}, {"x": 1549, "y": 1110}]}}, {"text": "-", "bounding_poly": {"vertices": [{"x": 1570, "y": 1102}, {"x": 1574, "y": 1102}, {"x": 1574, "y": 1109}, {"x": 1570, "y": 1109}]}}, {"text": "Italy", "bounding_poly": {"vertices": [{"x": 1575, "y": 1102}, {"x": 1590, "y": 1102}, {"x": 1590, "y": 1109}, {"x": 1575, "y": 1109}]}}]}, {"text": "EN 10025-1 \nEN 10025-2", "bounding_poly": {"vertices": [{"x": 1550, "y": 1131}, {"x": 1610, "y": 1131}, {"x": 1610, "y": 1153}, {"x": 1550, "y": 1153}]}, "elements": [{"text": "EN", "bounding_poly": {"vertices": [{"x": 1550, "y": 1131}, {"x": 1565, "y": 1131}, {"x": 1565, "y": 1139}, {"x": 1550, "y": 1139}]}}, {"text": "10025-1", "bounding_poly": {"vertices": [{"x": 1569, "y": 1131}, {"x": 1609, "y": 1131}, {"x": 1609, "y": 1139}, {"x": 1569, "y": 1139}]}}, {"text": "EN", "bounding_poly": {"vertices": [{"x": 1550, "y": 1144}, {"x": 1563, "y": 1144}, {"x": 1563, "y": 1153}, {"x": 1550, "y": 1153}]}}, {"text": "10025-2", "bounding_poly": {"vertices": [{"x": 1568, "y": 1144}, {"x": 1610, "y": 1145}, {"x": 1610, "y": 1154}, {"x": 1568, "y": 1153}]}}]}]}