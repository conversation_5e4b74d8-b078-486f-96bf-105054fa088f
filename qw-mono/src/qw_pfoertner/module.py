from typing import Any, Protocol

import falcon
import falcon.media.multipart

from qw_basic_iam_policy.policy import QwPolicy
from qw_basic_keycloak.interface import IdentityAccessManagement
from qw_basic_keycloak.openid.session import SessionTokenGateway
from qw_drawing_tolerance.module import QwDrawingToleranceModule
from qw_falcon_openapi.app import OpenApiRouting
from qw_falcon_openapi.generator import OpenApiDocumentGeneratorOptions
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.base import Base
from qw_pfoertner.api.internal.get_health import GetHealthController
from qw_pfoertner.api.internal.get_info import GetInfoController
from qw_pfoertner.api.v1.agent.process_agent_prompt import ProcessAgentPromptController
from qw_pfoertner.api.v1.chat.add_chat_message import AddChatMessageController
from qw_pfoertner.api.v1.chat.edit_chat_message import EditChatMessageController
from qw_pfoertner.api.v1.chat.get_chat_messages import GetChatMessagesController
from qw_pfoertner.api.v1.file.add_file_resource_revision import AddFileResourceRevisionController
from qw_pfoertner.api.v1.file.delete_file_resources import DeleteFileResourcesController
from qw_pfoertner.api.v1.file.drawing.add_drawing_discussion import AddDrawingDiscussionController
from qw_pfoertner.api.v1.file.drawing.get_drawing_discussion import GetDrawingDiscussionController
from qw_pfoertner.api.v1.file.drawing.get_drawing_revision_analysis import GetDrawingRevisionAnalysisController
from qw_pfoertner.api.v1.file.drawing.get_drawing_revision_analysis_status import (
    GetDrawingRevisionAnalysisStatusController,
)
from qw_pfoertner.api.v1.file.drawing.get_drawing_revision_page_image import GetDrawingRevisionPageImageController
from qw_pfoertner.api.v1.file.drawing.get_drawing_revision_page_tiles import GetDrawingRevisionPageTilesController
from qw_pfoertner.api.v1.file.drawing.list_drawing_discussions import ListDrawingDiscussionsController
from qw_pfoertner.api.v1.file.drawing.set_drawing_discussion_status import SetDrawingDiscussionStatusController
from qw_pfoertner.api.v1.file.get_file_resource import GetFileResourceController
from qw_pfoertner.api.v1.file.get_file_resource_download import GetFileResourceDownloadController
from qw_pfoertner.api.v1.file.get_file_resource_revision import GetFileResourceRevisionController
from qw_pfoertner.api.v1.file.link_file_to_material import LinkFileToMaterialController
from qw_pfoertner.api.v1.file.list_file_resources import ListFileResourcesController
from qw_pfoertner.api.v1.file.list_file_resources_metadata import ListFileResourcesMetadataController
from qw_pfoertner.api.v1.file.material_certificate.get_material_certificate_analysis import (
    GetMaterialCertificateAnalysisController,
)
from qw_pfoertner.api.v1.file.material_certificate.get_material_certificate_page_image import (
    GetMaterialCertificatePageImageController,
)
from qw_pfoertner.api.v1.file.rename_file_resource import RenameFileResourceController
from qw_pfoertner.api.v1.file.toggle_file_resource_revision_access import ToggleFileResourceRevisionAccessController
from qw_pfoertner.api.v1.file.unlink_file_from_material import UnlinkFileFromMaterialController
from qw_pfoertner.api.v1.inspection.add_inspection_action_result import AddInspectionActionResultController
from qw_pfoertner.api.v1.inspection.add_inspection_plan import AddInspectionPlanController
from qw_pfoertner.api.v1.inspection.delete_inspection_plans import DeleteInspectionPlansController
from qw_pfoertner.api.v1.inspection.delete_inspections import DeleteInspectionsController
from qw_pfoertner.api.v1.inspection.finish_inspection import FinishInspectionController
from qw_pfoertner.api.v1.inspection.get_inspection import GetInspectionController
from qw_pfoertner.api.v1.inspection.get_inspection_plan import GetInspectionPlanController
from qw_pfoertner.api.v1.inspection.get_inspection_result_as_pdf import GetInspectionResultAsPDFController
from qw_pfoertner.api.v1.inspection.get_inspection_result_image import GetInspectionResultStepImageController
from qw_pfoertner.api.v1.inspection.get_inspection_result_video import GetInspectionResultStepVideoController
from qw_pfoertner.api.v1.inspection.list_inspection_plans import ListInspectionPlansController
from qw_pfoertner.api.v1.inspection.list_inspection_plans_metadata import ListInspectionPlansMetadataController
from qw_pfoertner.api.v1.inspection.list_inspections import ListInspectionsController
from qw_pfoertner.api.v1.inspection.update_inspection_plan import UpdateInspectionPlanController
from qw_pfoertner.api.v1.material.get_material import GetMaterialController
from qw_pfoertner.api.v1.material.list_materials import ListMaterialsController
from qw_pfoertner.api.v1.order.add_order_line import AddOrderLineController
from qw_pfoertner.api.v1.order.add_order_line_task import AddOrderLineTaskController
from qw_pfoertner.api.v1.order.delete_order_lines import DeleteOrderLinesController
from qw_pfoertner.api.v1.order.delete_orders import DeleteOrdersController
from qw_pfoertner.api.v1.order.get_order import GetOrderController
from qw_pfoertner.api.v1.order.get_order_line import GetOrderLineController
from qw_pfoertner.api.v1.order.get_order_line_tasks import GetOrderLineTasksController
from qw_pfoertner.api.v1.order.get_purchase_order_connections import GetPurchaseOrderConnectionsController
from qw_pfoertner.api.v1.order.list_orders import ListOrdersController
from qw_pfoertner.api.v1.order.update_order_line_inspection_due_date import UpdateOrderLineInspectionDueDateController
from qw_pfoertner.api.v1.order.update_order_line_requirements import UpdateOrderLineRequirementsController
from qw_pfoertner.api.v1.search.get_search_matches import GetSearchMatchesController
from qw_pfoertner.api.v1.session.conclude_login import ConcludeLoginController, UserRecorder
from qw_pfoertner.api.v1.session.get_session import GetSessionInfoController
from qw_pfoertner.api.v1.session.initiate_login import InitiateLoginController
from qw_pfoertner.api.v1.session.initiate_logout import InitiateLogoutController
from qw_pfoertner.api.v1.tenant.get_tenant import GetTenantController
from qw_pfoertner.api.v1.tenant.get_tenant_details import GetTenantDetailsController
from qw_pfoertner.api.v1.tenant.list_tenants import ListTenantsController
from qw_pfoertner.api.v1.tolerance.get_tolerance_range import LookupToleranceController
from qw_pfoertner.api.v1.tolerance.list_tolerance_standards import GetTolerancesController
from qw_pfoertner.api.v1.user.get_user import GetUserController
from qw_pfoertner.api.v1.user.get_user_whoami import GetUserWhoamiController
from qw_pfoertner.api.v1.user.list_users import ListUsersController
from qw_pfoertner.api.v1.wopi.internal.wopi_discovery import WopiDiscoveryController
from qw_pfoertner.api.v1.wopi.internal.wopi_token import WopiAccessTokenController
from qw_pfoertner.api.v1.wopi.wopi_file import WopiFileController
from qw_pfoertner.api.v1.wopi.wopi_file_contents import WopiFileContentsController
from qw_pfoertner.config import QwPfoertnerConfig, QwPfoertnerSettings
from qw_pfoertner.middleware.service_account_auth import ServiceAccountAuthMiddleware
from qw_pfoertner.mp_util import FalconMultipartConfigUtil
from qw_pfoertner.service.service_account_auth import ServiceAccountAuthenticationService
from qw_pfoertner.service.session import SessionService
from qw_pfoertner.service.wopi_auth import WopiAuthenticationService
from qw_tenant_config.registry import TenantConfigRegistry
from qw_trunk.module import QwTrunkModule


class AppInfo(Protocol):
    name: str
    version: str
    commit: str


class QwPfoertnerModule(object):
    def __init__(
        self,
        iam: IdentityAccessManagement,
        session_service: SessionService,
        wopi_auth_service: WopiAuthenticationService,
        service_auth_service: ServiceAccountAuthenticationService,
        settings: QwPfoertnerSettings,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        self.iam = iam
        self.session_service = session_service
        self.wopi_auth_service = wopi_auth_service
        self.service_auth_service = service_auth_service
        self.settings = settings
        self.lf = lf

    @classmethod
    def from_config(
        cls, cfg: QwPfoertnerConfig, tenant_registry: TenantConfigRegistry, lf: LogFactory
    ) -> "QwPfoertnerModule":
        logger = lf.get_logger(__name__)

        rdb = cfg.db.build(Base, logger=logger)

        iam = cfg.iam.build(lf=lf)
        openid_client = iam.get_openid_client()
        token_inspector = iam.get_openid_token_inspector()
        token_gateway = SessionTokenGateway(cfg.tkn_gateway_salt, lf)
        policy = QwPolicy.build(token_inspector, tenant_registry, cfg.policy_settings, lf)

        return cls(
            iam=iam,
            session_service=SessionService(rdb, openid_client, token_gateway, policy, cfg.session_settings, lf),
            wopi_auth_service=WopiAuthenticationService(cfg.wopi_auth_settings, lf),
            service_auth_service=ServiceAccountAuthenticationService.from_config(cfg.service_auth_settings, lf),
            settings=cfg.settings,
            lf=lf,
        )

    def handle_uncaught_exception(self, req: falcon.Request, resp: falcon.Response, ex: Exception, params: Any) -> None:
        # https://github.com/falconry/falcon/issues/1942
        self.lf.get_logger(__name__).error(f"Caught unhandled exception '{ex}'", exc_info=ex)
        raise falcon.HTTPInternalServerError(title="Internal Server Error")

    def configure_app(
        self,
        app: falcon.App,
        app_info: AppInfo,
        tenant_registry: TenantConfigRegistry,
        trunk_module: QwTrunkModule,
        drawing_tolerance_module: QwDrawingToleranceModule,
    ) -> None:
        logger = self.lf.get_logger(__name__)

        # Add service account authentication middleware
        service_auth_middleware = ServiceAccountAuthMiddleware(self.service_auth_service, self.lf)
        app.add_middleware(service_auth_middleware)
        logger.info("Added service account authentication middleware")

        max_bp_buf_size = self.settings.max_body_part_buffer_size
        if max_bp_buf_size is not None:
            FalconMultipartConfigUtil.set_max_multipart_body_size(app, max_bp_buf_size)
            logger.info(
                f"Setting falcon multipart max_body_part_buffer_size "
                f"to {max_bp_buf_size} bytes ({max_bp_buf_size / (1000 * 1000):.2f}MB)"
            )

        opts = OpenApiDocumentGeneratorOptions(prepend_schema_name_in_descriptions=True)
        r = OpenApiRouting(app, app_info.version, app_info.name, generator_options=opts)

        agent = trunk_module.agent_service
        drw = trunk_module.drawing_service
        drw_analysis = trunk_module.drawing_analysis_client
        disc = trunk_module.drawing_discussion_service
        frs = trunk_module.file_service
        mat = trunk_module.material_service
        mat_db = mat.db_service
        mat_cert = trunk_module.material_certificate_service
        order = trunk_module.order_service
        order_db = trunk_module.order_service.order_db_service
        order_import = trunk_module.order_line_import_service
        order_task = trunk_module.order_line_task_service
        ins_plan = trunk_module.inspection_plan_service
        ins_res = trunk_module.inspection_result_service
        ins_acl = ins_plan.inspection_acl_service
        ins_db = ins_plan.inspection_db_service
        ins_pdf = trunk_module.inspection_result_pdf_generation_service
        tolerance_service = drawing_tolerance_module.tolerance_service
        user = trunk_module.user_service
        user_db = user.user_db_service
        chat = trunk_module.chat_db_service
        session = self.session_service
        wopi_auth = self.wopi_auth_service

        with r.prefix("/api/v1/"):
            with r.tag("session"):
                self.add_session_controllers(r, session, user_recorder=user, lf=self.lf)

            with r.tag("tenant"):
                r.add(GetTenantController(session, tenant_registry, self.lf))
                r.add(GetTenantDetailsController(session, tenant_registry, self.lf))
                r.add(ListTenantsController(session, tenant_registry, self.lf))

            with r.tag("user"):
                r.add(GetUserWhoamiController(session, user_db, tenant_registry, self.lf))
                r.add(GetUserController(session, user_db, self.lf))
                r.add(ListUsersController(session, user_db, self.lf))

            with r.tag("search"):
                r.add(GetSearchMatchesController(session, trunk_module.search_service, self.lf))

            with r.tag("file-resource"):
                r.add(AddFileResourceRevisionController(session, frs, drw, user_db, self.lf))
                r.add(GetFileResourceController(session, frs, tenant_registry, self.lf))
                r.add(GetFileResourceRevisionController(session, frs, frs.s3_service, self.lf))
                r.add(ListFileResourcesController(session, frs, tenant_registry, self.lf))
                r.add(ListFileResourcesMetadataController(session, frs, tenant_registry, self.lf))
                r.add(GetDrawingRevisionAnalysisController(session, frs, frs.s3_service, drw_analysis, self.lf))
                r.add(GetDrawingRevisionAnalysisStatusController(session, frs, drw_analysis, self.lf))
                r.add(GetDrawingRevisionPageImageController(session, frs, drw, self.lf))
                r.add(GetDrawingRevisionPageTilesController(session, frs, drw, self.lf))
                r.add(GetMaterialCertificateAnalysisController(session, frs, mat_cert, self.lf))
                r.add(GetMaterialCertificatePageImageController(session, frs, mat_cert, self.lf))
                r.add(AddDrawingDiscussionController(session, frs, disc, user_db, self.lf))
                r.add(GetDrawingDiscussionController(session, frs, disc, self.lf))
                r.add(SetDrawingDiscussionStatusController(session, frs, disc, self.lf))
                r.add(ListDrawingDiscussionsController(session, frs, disc, user_db, self.lf))
                r.add(RenameFileResourceController(session, frs, self.lf))
                r.add(DeleteFileResourcesController(session, frs, self.lf))
                r.add(GetFileResourceDownloadController(session, frs, frs.s3_service, self.lf))
                r.add(ToggleFileResourceRevisionAccessController(session, frs, self.lf))
                r.add(LinkFileToMaterialController(session, frs, self.lf))
                r.add(UnlinkFileFromMaterialController(session, frs, self.lf))

            with r.tag("material"):
                r.add(GetMaterialController(session, mat, tenant_registry, self.lf))
                r.add(ListMaterialsController(session, mat_db, tenant_registry, self.lf))

            with r.tag("order"):
                r.add(AddOrderLineController(session, order_import, user_db, tenant_registry, self.lf))
                r.add(GetOrderController(session, order_db, tenant_registry, self.lf))
                r.add(GetOrderLineController(session, order_db, self.lf))
                r.add(ListOrdersController(session, order_db, tenant_registry, self.lf))
                r.add(DeleteOrdersController(session, order, self.lf))
                r.add(DeleteOrderLinesController(session, order, self.lf))
                r.add(GetPurchaseOrderConnectionsController(session, order_db, tenant_registry, self.lf))
                r.add(AddOrderLineTaskController(session, order_task, user_db, self.lf))
                r.add(GetOrderLineTasksController(session, order_task, user_db, tenant_registry, self.lf))
                r.add(UpdateOrderLineInspectionDueDateController(session, order_task, user_db, self.lf))
                r.add(UpdateOrderLineRequirementsController(session, order, order_db, self.lf))

            with r.tag("inspection"):
                r.add(AddInspectionPlanController(session, ins_plan, order_db, user_db, self.lf))
                r.add(GetInspectionPlanController(session, ins_plan, tenant_registry, self.lf))
                r.add(ListInspectionPlansController(session, ins_db, tenant_registry, self.lf))
                r.add(ListInspectionPlansMetadataController(session, ins_plan, tenant_registry, self.lf))
                r.add(AddInspectionActionResultController(session, ins_res, ins_acl, user_db, tenant_registry, self.lf))
                r.add(GetInspectionController(session, ins_res, ins_acl, user_db, tenant_registry, self.lf))
                r.add(FinishInspectionController(session, ins_res, ins_acl, user_db, self.lf))
                r.add(GetInspectionResultStepImageController(session, ins_res, ins_acl, self.lf))
                r.add(GetInspectionResultStepVideoController(session, ins_res, ins_acl, self.lf))
                r.add(
                    GetInspectionResultAsPDFController(
                        session, ins_res, ins_acl, ins_pdf, user_db, tenant_registry, self.lf
                    )
                )
                r.add(ListInspectionsController(session, ins_db, user_db, tenant_registry, self.lf))
                r.add(UpdateInspectionPlanController(session, ins_plan, order_db, user_db, self.lf))
                r.add(DeleteInspectionsController(session, ins_res, self.lf))
                r.add(DeleteInspectionPlansController(session, ins_plan, self.lf))

            with r.tag("agent"):
                r.add(ProcessAgentPromptController(agent, session, self.lf))

            with r.tag("chat"):
                r.add(GetChatMessagesController(chat, user_db, session, self.lf))
                r.add(EditChatMessageController(chat, user_db, session, self.lf))
                r.add(AddChatMessageController(chat, user_db, session, self.lf))

            with r.tag("wopi"):
                r.add(WopiDiscoveryController(session, trunk_module.wopi_service, self.lf))
                r.add(WopiAccessTokenController(session, wopi_auth, frs, self.lf))
                r.add(WopiFileController(session, wopi_auth, user_db, frs, self.lf))
                r.add(WopiFileContentsController(session, wopi_auth, frs, self.lf))

            with r.tag("tolerances"):
                r.add(GetTolerancesController(session, tolerance_service, self.lf))
                r.add(LookupToleranceController(session, tolerance_service, self.lf))

        with r.prefix("/api/"):
            with r.tag("internal"):
                r.add(
                    GetInfoController(
                        app_info.version,
                        app_info.commit,
                        FalconMultipartConfigUtil.get_max_multipart_body_size(app),
                        self.lf,
                    )
                )
                r.add(GetHealthController(self.iam, trunk_module.health_check_service, self.lf))

        if self.settings.include_spec_route:
            with r.tag("spec"):
                r.add_spec_route("/api/openapi")

        # app.add_error_handler(Exception, self.handle_uncaught_exception)

    @classmethod
    def add_session_controllers(
        cls,
        r: OpenApiRouting,
        session: SessionService,
        user_recorder: UserRecorder | None = None,
        lf: LogFactory = NO_LOG_FACTORY,
    ) -> None:
        # this function is moved out for testing purposes
        r.add(InitiateLoginController(session, lf=lf))
        r.add(ConcludeLoginController(session, user_recorder, lf=lf))
        r.add(GetSessionInfoController(session, lf=lf))
        r.add(InitiateLogoutController(session, lf=lf))
