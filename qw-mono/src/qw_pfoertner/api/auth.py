from dataclasses import dataclass
from typing import cast, Any, BinaryIO, Dict, Final, Sequence, Type, Union

import falcon

from qw_basic_iam_policy.interface import AuthPolicy
from qw_basic_iam_policy.service_account import ServiceAccountAuthContext
from qw_basic_iam_policy.token import ValidAccessToken
from qw_falcon_openapi.multipart import BodyParts, MultipartBodyDefinition, OpenApiOpMultipartInJsonOut
from qw_falcon_openapi.operation import (
    T_HEADERS,
    T_IN,
    T_OUT,
    T_PATH_PARAMS,
    T_QUERY_PARAMS,
    OpenApiOpBinaryInJsonOut,
    OpenApiOpBinaryOut,
    OpenApiOpBinaryOutput,
    OpenApiOpJsonInBinaryOut,
    OpenApiOpJsonInJsonOut,
    OpenApiOpJsonOut,
    OpenApiOpOutput,
    OpenApiOpParams,
)
from qw_falcon_openapi.spec import OpenApiApiKeySecurityScheme, OpenApiMimeType, OpenApiParameterType
from qw_log_interface import Logger
from qw_pfoertner.service.service_account_auth import ServiceAccountAuthenticationService
from qw_pfoertner.service.session import SessionService


SCHEME_SESSION = OpenApiApiKeySecurityScheme(
    name="session_token",
    **cast(Dict[str, Any], {"in": OpenApiParameterType.COOKIE}),
)
# SCHEME_CSRF = OpenApiApiKeySecurityScheme(name="csrf_token", in_=OpenApiParameterType.HEADER)

COOKIE_SESSION: Final[str] = "session"
# COOKIE_CSRF: Final[str] = "csrf"
ERROR_401 = "Session parameter is missing or invalid in some way"
ERROR_403 = "Requested resource or action is not available for this user"


@dataclass
class AuthContextData:
    session_uuid: str
    tkn: ValidAccessToken


class AuthContext(object):
    def __init__(self, data: Union[AuthContextData, ServiceAccountAuthContext], logger: Logger):
        self.logger = logger
        self.__data = data
        self.__submitted_policy: AuthPolicy | None = None
        self.allow_multi_eval = False

    @property
    def session_uuid(self) -> str:
        if isinstance(self.__data, AuthContextData):
            return self.__data.session_uuid
        else:
            # Service account doesn't have session UUID
            return ""

    @property
    def tkn(self) -> ValidAccessToken:
        if isinstance(self.__data, AuthContextData):
            return self.__data.tkn
        else:
            raise AttributeError("Service account context doesn't have user token")

    @property
    def is_service_account(self) -> bool:
        return isinstance(self.__data, ServiceAccountAuthContext)

    @property
    def service_name(self) -> str:
        if isinstance(self.__data, ServiceAccountAuthContext):
            return self.__data.service_name
        else:
            raise AttributeError("User session context doesn't have service name")

    def eval_policy(self, policy: AuthPolicy) -> None:
        if self.__submitted_policy is not None and not self.allow_multi_eval:
            raise ValueError("A policy was already submitted before")
        self.__submitted_policy = policy

        # Handle different policy types
        if isinstance(self.__data, ServiceAccountAuthContext):
            # For service accounts, check if policy supports service account evaluation
            if hasattr(policy, 'eval') and hasattr(policy, '__class__'):
                # Import here to avoid circular imports
                from qw_basic_iam_policy.service_account import AllowServiceAccount, AllowUserOrService

                if isinstance(policy, (AllowServiceAccount, AllowUserOrService)):
                    # Policy supports service account context
                    policy_result = policy.eval(self.__data, self.logger)
                else:
                    # Traditional policy doesn't support service accounts
                    self.logger.error(f"Policy {policy.__class__.__name__} doesn't support service accounts")
                    policy_result = False
            else:
                policy_result = False
        else:
            # Traditional user session evaluation
            policy_result = policy.eval(self.__data.tkn, self.logger)

        if not policy_result:
            raise falcon.HTTPForbidden(
                title="Forbidden", description="You don't have permission to access this resource"
            )

    def was_policy_evaluated(self) -> bool:
        return self.__submitted_policy is not None

    def __enter__(self) -> "AuthContext":
        return self

    def __exit__(self, exc_type: Any, exc_val: Any, exc_tb: Any) -> None:
        if exc_type is None and not self.was_policy_evaluated():
            self.logger.error("No policy was evaluated, defaulting to 403")
            raise falcon.HTTPForbidden(title="Unauthorized", description="Session token required")
        # do not suppress any exceptions that happened between __enter__ and __exit__, None will be evaluated as False
        # https://mypy.readthedocs.io/en/stable/error_code_list.html#check-the-return-type-of-exit-exit-return


def get_session_token_from_cookies(cookies: Dict[str, str]) -> str | None:
    return cookies.get(COOKIE_SESSION)


def get_access_token_and_verify_from_cookies(
    session_service: SessionService, cookies: Dict[str, str]
) -> AuthContextData:
    session_token = get_session_token_from_cookies(cookies)
    if session_token is None:
        raise falcon.HTTPUnauthorized(title="Unauthorized", description="Invalid session")
    return get_access_token_and_verify_from_session(session_service, session_token)


def get_access_token_and_verify_from_session(session_service: SessionService, session_token: str) -> AuthContextData:
    result = session_service.find_and_validate_session(session_token)
    if result is None:
        raise falcon.HTTPUnauthorized(title="Unauthorized", description="Session token is invalid")
    valid_token = session_service.policy.inspect_access_token(result.access_token)
    if valid_token is None:
        raise falcon.HTTPUnauthorized(title="Unauthorized", description="Invalid access token")
    return AuthContextData(result.session_uuid, valid_token)


def get_auth_context_from_request_context(
    session_service: SessionService,
    cookies: Dict[str, str]
) -> Union[AuthContextData, ServiceAccountAuthContext]:
    """
    Get authentication context from thread-local context or session cookies.

    Priority:
    1. Check for service account context in thread-local storage (set by middleware)
    2. Fall back to session cookie (user session)
    """
    # Import here to avoid circular imports
    from qw_pfoertner.context.request_context import get_service_account_auth

    # Check if service account authentication was already handled by middleware
    service_account_auth = get_service_account_auth()
    if service_account_auth:
        return service_account_auth

    # Fall back to session-based authentication
    return get_access_token_and_verify_from_cookies(session_service, cookies)


def get_auth_context_from_headers_and_cookies(
    session_service: SessionService,
    service_auth_service: ServiceAccountAuthenticationService | None,
    headers_obj: Any,
    cookies: Dict[str, str]
) -> Union[AuthContextData, ServiceAccountAuthContext]:
    """
    Get authentication context from either session cookies or Authorization Bearer tokens.

    Priority:
    1. Check for Authorization Bearer token (service account) - if service_auth_service is provided
    2. Fall back to session cookie (user session)

    Note: This function is kept for backward compatibility. The preferred approach
    is to use middleware for service account authentication.
    """
    # Try to extract authorization header from the headers object
    auth_header = None
    if hasattr(headers_obj, 'authorization'):
        auth_header = headers_obj.authorization
    elif hasattr(headers_obj, '__dict__'):
        # Check if it's a pydantic model with authorization field
        auth_header = getattr(headers_obj, 'authorization', None)

    # Check for Authorization Bearer token first (only if service auth is available)
    if service_auth_service and auth_header and auth_header.startswith("Bearer "):
        token = auth_header[7:]  # Remove "Bearer " prefix
        service_auth_context = service_auth_service.validate_service_token(token)
        if service_auth_context:
            return service_auth_context
        # If Bearer token is present but invalid, don't fall back to session
        raise falcon.HTTPUnauthorized(title="Unauthorized", description="Invalid service account token")

    # Fall back to session-based authentication
    return get_access_token_and_verify_from_cookies(session_service, cookies)


class AuthOpenApiOpJsonOut(OpenApiOpJsonOut[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(t_out, t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(self, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]) -> OpenApiOpOutput[T_OUT]:
        # Try thread-local context first (for service accounts), then fall back to headers/cookies
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, params)

    def on_request_after_auth(
        self, auth_ctx: AuthContext, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class AuthOpenApiOpJsonInJsonOut(OpenApiOpJsonInJsonOut[T_IN, T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_in: Type[T_IN],
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(t_in, t_out, t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        # self.spec_options.add_security_scheme(SCHEME_CSRF)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(
        self, body: T_IN, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, body, params)

    def on_request_after_auth(
        self,
        auth_ctx: AuthContext,
        body: T_IN,
        params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS],
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class AuthOpenApiOpBinaryInJsonOut(OpenApiOpBinaryInJsonOut[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        mime_types_in: Sequence[OpenApiMimeType],
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(mime_types_in, t_out, t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        # self.spec_options.add_security_scheme(SCHEME_CSRF)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(
        self, body: BinaryIO, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, body, params)

    def on_request_after_auth(
        self,
        auth_ctx: AuthContext,
        body: BinaryIO,
        params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS],
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()


class AuthOpenApiOpJsonInBinaryOut(OpenApiOpJsonInBinaryOut[T_IN, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_in: Type[T_IN],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(t_in, t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(
        self, body: T_IN, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpBinaryOutput:
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, body, params)

    def on_request_after_auth(
        self,
        auth_ctx: AuthContext,
        body: T_IN,
        params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS],
    ) -> OpenApiOpBinaryOutput:
        raise NotImplementedError()


class AuthOpenApiOpBinaryOut(OpenApiOpBinaryOut[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(self, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]) -> OpenApiOpBinaryOutput:
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, params)

    def on_request_after_auth(
        self,
        auth_ctx: AuthContext,
        params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS],
    ) -> OpenApiOpBinaryOutput:
        raise NotImplementedError()


class AuthOpenApiOpMultipartInJsonOut(OpenApiOpMultipartInJsonOut[T_OUT, T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]):
    def __init__(
        self,
        def_in: MultipartBodyDefinition,
        t_out: Type[T_OUT],
        t_params_path: Type[T_PATH_PARAMS],
        t_params_query: Type[T_QUERY_PARAMS],
        t_params_headers: Type[T_HEADERS],
        session_service: SessionService,
        logger: Logger,
        service_auth_service: ServiceAccountAuthenticationService | None = None,
    ):
        super().__init__(def_in, t_out, t_params_path, t_params_query, t_params_headers)
        self.logger = logger
        self.spec_options.add_security_scheme(SCHEME_SESSION)
        self.spec_options.responses_error[401] = ERROR_401
        self.spec_options.responses_error[403] = ERROR_403
        self.session_service = session_service
        self.service_auth_service = service_auth_service

    def on_request(
        self, body_parts: BodyParts, params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS]
    ) -> OpenApiOpOutput[T_OUT]:
        auth_ctx_data = get_auth_context_from_request_context(self.session_service, params.cookies)
        with AuthContext(auth_ctx_data, self.logger) as auth_ctx:
            return self.on_request_after_auth(auth_ctx, body_parts, params)

    def on_request_after_auth(
        self,
        auth_ctx: AuthContext,
        body_parts: BodyParts,
        params: OpenApiOpParams[T_PATH_PARAMS, T_QUERY_PARAMS, T_HEADERS],
    ) -> OpenApiOpOutput[T_OUT]:
        raise NotImplementedError()
