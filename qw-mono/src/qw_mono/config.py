from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_log.factory import QwLogConfig
from qw_pfoertner.config import QwPfoertnerBaseConfig, QwPfoertnerConfig
from qw_tenant_config.registry import QwTenantsConfig
from qw_trunk.config import QwTrunkBaseConfig, QwTrunkConfig


class QwMonoConfig(BaseModel):
    mono_db: RelationalDatabaseConfig
    mono_s3: S3StorageConfig
    mono_pfoertner: QwPfoertnerBaseConfig
    mono_trunk: QwTrunkBaseConfig
    tenants: QwTenantsConfig
    logging: QwLogConfig

    @property
    def pfoertner(self) -> QwPfoertnerConfig:
        return QwPfoertnerConfig(
            tkn_gateway_salt=self.mono_pfoertner.tkn_gateway_salt,
            iam=self.mono_pfoertner.iam,
            session_settings=self.mono_pfoertner.session_settings,
            wopi_auth_settings=self.mono_pfoertner.wopi_auth_settings,
            service_auth_settings=self.mono_pfoertner.service_auth_settings,
            policy_settings=self.mono_pfoertner.policy_settings,
            settings=self.mono_pfoertner.settings,
            db=self.mono_db,
        )

    @property
    def trunk(self) -> QwTrunkConfig:
        return QwTrunkConfig(
            drawing_settings=self.mono_trunk.drawing_settings,
            chat_db_settings=self.mono_trunk.chat_db_settings,
            wopi_settings=self.mono_trunk.wopi_settings,
            api_keys=self.mono_trunk.api_keys,
            db=self.mono_db,
            s3=self.mono_s3,
        )
